
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { LogIn } from 'lucide-react';

interface AdminAuthenticationProps {
  onLogin: (userRole: 'super_admin' | 'admin' | 'viewer') => void;
}

export const AdminAuthentication = ({ onLogin }: AdminAuthenticationProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  // Super Admin credentials
  const SUPER_ADMIN = {
    email: '<EMAIL>',
    password: 'Lova@12345'
  };

  // Demo admin accounts (in production, this would be from database)
  const adminAccounts = [
    { email: '<EMAIL>', password: 'admin123', role: 'admin' as const },
    { email: '<EMAIL>', password: 'viewer123', role: 'viewer' as const }
  ];

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Check super admin
    if (email === SUPER_ADMIN.email && password === SUPER_ADMIN.password) {
      onLogin('super_admin');
      return;
    }

    // Check admin accounts
    const account = adminAccounts.find(acc => acc.email === email && acc.password === password);
    if (account) {
      onLogin(account.role);
      return;
    }

    setError('Invalid credentials. Contact super admin for access.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-500 via-purple-600 to-blue-700 flex items-center justify-center">
      <Card className="w-full max-w-md p-6 bg-white/20 backdrop-blur-sm border-white/30">
        <div className="text-center mb-6">
          <img 
            src="/lovable-uploads/649f40b2-e2bb-4999-95a9-76ba7fc7c5c6.png" 
            alt="Logo" 
            className="w-16 h-16 mx-auto mb-4"
          />
          <h1 className="text-2xl font-bold text-white"> Admin</h1>
          <p className="text-white/80 mt-2">Secure Administrative Access</p>
        </div>

        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <Label htmlFor="email" className="text-white">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              className="bg-white/20 border-white/30 text-white placeholder:text-white/60"
            />
          </div>
          <div>
            <Label htmlFor="password" className="text-white">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              className="bg-white/20 border-white/30 text-white placeholder:text-white/60"
            />
          </div>
          
          {error && (
            <div className="p-3 bg-red-500/20 border border-red-500/30 rounded text-red-200 text-sm">
              {error}
            </div>
          )}

          <Button type="submit" className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
            <LogIn className="w-4 h-4 mr-2" />
            Secure Login
          </Button>
        </form>

        <div className="mt-6 p-3 bg-white/20 rounded-lg border border-white/30">
          <p className="text-sm text-white mb-2">
            <strong>Demo Access Levels:</strong>
          </p>
          <div className="text-xs text-white/80 space-y-1">
            <div>Super Admin: Full system access</div>
            <div>Admin: Management access</div>
            <div>Viewer: Read-only access</div>
          </div>
        </div>
      </Card>
    </div>
  );
};
