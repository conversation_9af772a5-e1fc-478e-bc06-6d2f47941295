
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Line, <PERSON><PERSON>hart, <PERSON>, Cell } from 'recharts';
import { Brain, Target, TrendingUp, AlertTriangle, Clock, MessageSquare } from 'lucide-react';

export const AdvancedAnalytics = () => {
  const intentData = [
    { intent: 'Account Help', accuracy: 92, count: 45 },
    { intent: 'Billing', accuracy: 88, count: 32 },
    { intent: 'Technical Support', accuracy: 95, count: 28 },
    { intent: 'General Inquiry', accuracy: 85, count: 67 }
  ];

  const sentimentData = [
    { time: '09:00', positive: 75, neutral: 20, negative: 5 },
    { time: '12:00', positive: 65, neutral: 25, negative: 10 },
    { time: '15:00', positive: 70, neutral: 22, negative: 8 },
    { time: '18:00', positive: 60, neutral: 30, negative: 10 }
  ];

  const keywordData = [
    { word: 'account', count: 89, color: '#8884d8' },
    { word: 'billing', count: 67, color: '#82ca9d' },
    { word: 'help', count: 145, color: '#ffc658' },
    { word: 'support', count: 78, color: '#ff7300' }
  ];

  return (
    <div className="space-y-8">
      {/* Intent Recognition */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
          <Target className="w-5 h-5 mr-2" />
          Intent Recognition Accuracy
        </h3>
        <div className="space-y-4">
          {intentData.map((item) => (
            <div key={item.intent} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex justify-between mb-1">
                  <span className="text-white text-sm">{item.intent}</span>
                  <span className="text-white text-sm">{item.accuracy}%</span>
                </div>
                <Progress value={item.accuracy} className="h-2" />
              </div>
              <Badge variant="outline" className="ml-4 text-gray-300 border-gray-600">
                {item.count} calls
              </Badge>
            </div>
          ))}
        </div>
      </Card>

      {/* Sentiment Analysis */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" />
          Sentiment Over Time
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={sentimentData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis dataKey="time" stroke="#9CA3AF" />
            <YAxis stroke="#9CA3AF" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937', 
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F3F4F6'
              }} 
            />
            <Line type="monotone" dataKey="positive" stroke="#10B981" strokeWidth={2} />
            <Line type="monotone" dataKey="neutral" stroke="#F59E0B" strokeWidth={2} />
            <Line type="monotone" dataKey="negative" stroke="#EF4444" strokeWidth={2} />
          </LineChart>
        </ResponsiveContainer>
      </Card>

      {/* Keyword Heatmap */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
          <MessageSquare className="w-5 h-5 mr-2" />
          Keyword Heatmap
        </h3>
        <ResponsiveContainer width="100%" height={200}>
          <BarChart data={keywordData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis dataKey="word" stroke="#9CA3AF" />
            <YAxis stroke="#9CA3AF" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937', 
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F3F4F6'
              }} 
            />
            <Bar dataKey="count" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      {/* Performance Metrics */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-6 bg-gray-800/50 border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Brain className="w-5 h-5 mr-2" />
            AI Confidence Scores
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">High Confidence (90%+)</span>
              <Badge className="bg-green-600">67%</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Medium Confidence (70-90%)</span>
              <Badge className="bg-yellow-600">28%</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Low Confidence (under 70%)</span>
              <Badge className="bg-red-600">5%</Badge>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-gray-800/50 border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Silence Analysis
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Avg User Pause</span>
              <span className="text-white">2.1s</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Avg AI Response Time</span>
              <span className="text-white">1.8s</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Interruption Rate</span>
              <Badge className="bg-orange-600">12%</Badge>
            </div>
          </div>
        </Card>
      </div>

      {/* Alerts */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          Real-Time Anomalies
        </h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between p-3 bg-yellow-600/20 rounded-lg border border-yellow-600/30">
            <span className="text-yellow-200">Increased response time detected</span>
            <Badge variant="outline" className="border-yellow-600 text-yellow-200">Warning</Badge>
          </div>
          <div className="flex items-center justify-between p-3 bg-red-600/20 rounded-lg border border-red-600/30">
            <span className="text-red-200">High number of call transfers in last hour</span>
            <Badge variant="outline" className="border-red-600 text-red-200">Critical</Badge>
          </div>
        </div>
      </Card>
    </div>
  );
};
