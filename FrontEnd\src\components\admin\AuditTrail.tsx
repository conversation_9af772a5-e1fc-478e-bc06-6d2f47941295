
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Filter, Download, Search, Shield, Eye, Edit, Trash2, Settings } from 'lucide-react';

export const AuditTrail = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const auditLogs = [
    {
      id: 'AUD-2024-001',
      timestamp: '2024-01-15 14:32:15',
      user: '<EMAIL>',
      action: 'Modified AI Settings',
      resource: 'Voice Configuration',
      details: 'Updated response timeout to 30s',
      severity: 'medium',
      ip: '*************'
    },
    {
      id: 'AUD-2024-002',
      timestamp: '2024-01-15 14:28:32',
      user: '<EMAIL>',
      action: 'Viewed Call Logs',
      resource: 'Call Data',
      details: 'Accessed sensitive call recordings',
      severity: 'low',
      ip: '*************'
    },
    {
      id: 'AUD-2024-003',
      timestamp: '2024-01-15 14:25:18',
      user: '<EMAIL>',
      action: 'Deleted User Data',
      resource: 'User Management',
      details: 'Removed expired user account: user123',
      severity: 'high',
      ip: '*************'
    },
    {
      id: 'AUD-2024-004',
      timestamp: '2024-01-15 14:20:45',
      user: '<EMAIL>',
      action: 'Updated Compliance Settings',
      resource: 'GDPR Configuration',
      details: 'Enabled data anonymization for EU users',
      severity: 'high',
      ip: '*************'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-500/20 text-red-400';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400';
      case 'low': return 'bg-green-500/20 text-green-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getActionIcon = (action: string) => {
    if (action.includes('View')) return <Eye className="w-4 h-4" />;
    if (action.includes('Modified') || action.includes('Updated')) return <Edit className="w-4 h-4" />;
    if (action.includes('Deleted')) return <Trash2 className="w-4 h-4" />;
    return <Settings className="w-4 h-4" />;
  };

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="w-6 h-6 text-blue-400" />
          <h3 className="text-xl font-semibold text-white">Audit Trail</h3>
          <Badge variant="outline" className="text-blue-400 border-blue-400">
            {auditLogs.length} Events
          </Badge>
        </div>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search audit logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-900 border-gray-700 text-white w-64"
            />
          </div>
          <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-700 hover:bg-transparent">
              <TableHead className="text-gray-300">Event ID</TableHead>
              <TableHead className="text-gray-300">Timestamp</TableHead>
              <TableHead className="text-gray-300">User</TableHead>
              <TableHead className="text-gray-300">Action</TableHead>
              <TableHead className="text-gray-300">Resource</TableHead>
              <TableHead className="text-gray-300">Severity</TableHead>
              <TableHead className="text-gray-300">IP Address</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {auditLogs.map((log) => (
              <TableRow key={log.id} className="border-gray-800 hover:bg-gray-900/30">
                <TableCell className="text-white font-mono">{log.id}</TableCell>
                <TableCell className="text-gray-300">{log.timestamp}</TableCell>
                <TableCell className="text-gray-300">{log.user}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {getActionIcon(log.action)}
                    <span className="text-gray-300">{log.action}</span>
                  </div>
                </TableCell>
                <TableCell className="text-gray-300">{log.resource}</TableCell>
                <TableCell>
                  <Badge className={getSeverityColor(log.severity)}>
                    {log.severity.toUpperCase()}
                  </Badge>
                </TableCell>
                <TableCell className="text-gray-300 font-mono">{log.ip}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </Card>
  );
};
