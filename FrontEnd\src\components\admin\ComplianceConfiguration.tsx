
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Settings, 
  Database, 
  Clock, 
  MapPin, 
  Lock,
  FileText,
  AlertTriangle,
  CheckCircle,
  Save
} from 'lucide-react';

export const ComplianceConfiguration = () => {
  const [retentionSettings, setRetentionSettings] = useState({
    voiceData: '30',
    transcripts: '90',
    personalData: '1825', // 5 years
    analyticsData: '365'
  });

  const [privacySettings, setPrivacySettings] = useState({
    explicitConsent: true,
    anonymization: true,
    dataMinimization: true,
    rightToErasure: true,
    dataPortability: true,
    consentWithdrawal: true
  });

  const [auditSettings, setAuditSettings] = useState({
    dataAccess: true,
    processingActivities: true,
    consentChanges: true,
    dataExports: true,
    deletionRequests: true
  });

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center space-x-3 mb-6">
        <Shield className="w-6 h-6 text-blue-400" />
        <h3 className="text-xl font-semibold text-white">Compliance Configuration</h3>
        <Badge variant="outline" className="text-green-400 border-green-400">
          Enterprise Ready
        </Badge>
      </div>

      <Tabs defaultValue="retention" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-900/50">
          <TabsTrigger value="retention" className="text-white">Data Retention</TabsTrigger>
          <TabsTrigger value="privacy" className="text-white">Privacy Controls</TabsTrigger>
          <TabsTrigger value="audit" className="text-white">Audit Settings</TabsTrigger>
          <TabsTrigger value="regional" className="text-white">Regional Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="retention" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-white flex items-center">
                <Clock className="w-5 h-5 mr-2 text-blue-400" />
                Data Retention Periods
              </h4>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="voice-retention" className="text-gray-300">Voice & Audio Data (days)</Label>
                  <Input
                    id="voice-retention"
                    type="number"
                    value={retentionSettings.voiceData}
                    onChange={(e) => setRetentionSettings({...retentionSettings, voiceData: e.target.value})}
                    className="bg-gray-900 border-gray-700 text-gray-300"
                    min="1"
                    max="90"
                  />
                  <p className="text-xs text-gray-400 mt-1">GDPR recommendation: 30 days max for voice data</p>
                </div>

                <div>
                  <Label htmlFor="transcript-retention" className="text-gray-300">Conversation Transcripts (days)</Label>
                  <Input
                    id="transcript-retention"
                    type="number"
                    value={retentionSettings.transcripts}
                    onChange={(e) => setRetentionSettings({...retentionSettings, transcripts: e.target.value})}
                    className="bg-gray-900 border-gray-700 text-gray-300"
                    min="1"
                    max="365"
                  />
                </div>

                <div>
                  <Label htmlFor="personal-retention" className="text-gray-300">Personal Data (days)</Label>
                  <Input
                    id="personal-retention"
                    type="number"
                    value={retentionSettings.personalData}
                    onChange={(e) => setRetentionSettings({...retentionSettings, personalData: e.target.value})}
                    className="bg-gray-900 border-gray-700 text-gray-300"
                    min="1"
                  />
                  <p className="text-xs text-gray-400 mt-1">Business requirement: Usually 5-7 years</p>
                </div>

                <div>
                  <Label htmlFor="analytics-retention" className="text-gray-300">Analytics Data (days)</Label>
                  <Input
                    id="analytics-retention"
                    type="number"
                    value={retentionSettings.analyticsData}
                    onChange={(e) => setRetentionSettings({...retentionSettings, analyticsData: e.target.value})}
                    className="bg-gray-900 border-gray-700 text-gray-300"
                    min="1"
                    max="1095"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-lg font-medium text-white flex items-center">
                <Database className="w-5 h-5 mr-2 text-blue-400" />
                Auto-Deletion Rules
              </h4>
              
              <div className="p-4 bg-gray-900/50 rounded-lg border border-gray-700">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Enable automatic deletion</span>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Notify before deletion</span>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">Grace period (days)</span>
                    <Input type="number" defaultValue="7" className="w-20 bg-gray-800 border-gray-600 text-gray-300" />
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <h5 className="text-blue-400 font-medium mb-2">Compliance Notes</h5>
                <ul className="text-sm text-blue-100 space-y-1">
                  <li>• GDPR Art. 17: Right to erasure implementation</li>
                  <li>• EU AI Act: AI training data lifecycle management</li>
                  <li>• DORA: Operational data retention for resilience</li>
                  <li>• HIPAA: PHI minimum necessary standard</li>
                </ul>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="privacy" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-white flex items-center">
                <Lock className="w-5 h-5 mr-2 text-blue-400" />
                Privacy Controls
              </h4>
              
              <div className="space-y-4">
                {Object.entries(privacySettings).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-gray-900/50 rounded">
                    <div>
                      <div className="text-sm text-white capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </div>
                      <div className="text-xs text-gray-400">
                        {key === 'explicitConsent' && 'Require explicit user consent for data processing'}
                        {key === 'anonymization' && 'Automatically anonymize data for analytics'}
                        {key === 'dataMinimization' && 'Collect only necessary data'}
                        {key === 'rightToErasure' && 'Enable right to be forgotten'}
                        {key === 'dataPortability' && 'Allow data export in machine-readable format'}
                        {key === 'consentWithdrawal' && 'Easy consent withdrawal mechanism'}
                      </div>
                    </div>
                    <Switch 
                      checked={value}
                      onCheckedChange={(checked) => 
                        setPrivacySettings({...privacySettings, [key]: checked})
                      }
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-lg font-medium text-white flex items-center">
                <MapPin className="w-5 h-5 mr-2 text-blue-400" />
                Data Localization
              </h4>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="primary-region" className="text-gray-300">Primary Data Region</Label>
                  <Select defaultValue="eu-central">
                    <SelectTrigger className="bg-gray-900 border-gray-700 text-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="eu-central">EU Central (Frankfurt)</SelectItem>
                      <SelectItem value="eu-west">EU West (Ireland)</SelectItem>
                      <SelectItem value="eu-north">EU North (Stockholm)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="backup-region" className="text-gray-300">Backup Region</Label>
                  <Select defaultValue="eu-west">
                    <SelectTrigger className="bg-gray-900 border-gray-700 text-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="eu-west">EU West (Ireland)</SelectItem>
                      <SelectItem value="eu-north">EU North (Stockholm)</SelectItem>
                      <SelectItem value="eu-south">EU South (Milan)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="p-3 bg-green-500/10 border border-green-500/20 rounded">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-green-400">GDPR Article 44-49 Compliant</span>
                  </div>
                  <p className="text-xs text-green-300 mt-1">All data remains within EU jurisdiction</p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-white flex items-center">
              <FileText className="w-5 h-5 mr-2 text-blue-400" />
              Audit Logging Configuration
            </h4>
            
            <div className="grid md:grid-cols-2 gap-4">
              {Object.entries(auditSettings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-3 bg-gray-900/50 rounded">
                  <div>
                    <div className="text-sm text-white capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()} Logging
                    </div>
                    <div className="text-xs text-gray-400">
                      Log all {key.replace(/([A-Z])/g, ' $1').toLowerCase()} activities
                    </div>
                  </div>
                  <Switch 
                    checked={value}
                    onCheckedChange={(checked) => 
                      setAuditSettings({...auditSettings, [key]: checked})
                    }
                  />
                </div>
              ))}
            </div>

            <div className="mt-6">
              <Label htmlFor="audit-retention" className="text-gray-300">Audit Log Retention (years)</Label>
              <Input
                id="audit-retention"
                type="number"
                defaultValue="7"
                className="bg-gray-900 border-gray-700 text-gray-300 w-32"
                min="1"
                max="10"
              />
              <p className="text-xs text-gray-400 mt-1">Legal requirement: Minimum 6 years in Germany</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="regional" className="space-y-4">
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-white">Regional Compliance Settings</h4>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-gray-900/50 rounded-lg border border-gray-700">
                  <h5 className="font-medium text-white mb-3">German Federal Requirements</h5>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">BSI IT-Grundschutz compliance</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">BDSG (Federal Data Protection Act)</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">TMG (Telemedia Act)</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-gray-900/50 rounded-lg border border-gray-700">
                  <h5 className="font-medium text-white mb-3">Industry-Specific</h5>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">Financial Services (BaFin)</span>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">Healthcare (SGB V)</span>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">Insurance (VAG)</span>
                      <Switch />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                  <h5 className="text-blue-400 font-medium mb-2">Compliance Status Summary</h5>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-blue-100">GDPR</span>
                      <Badge className="bg-green-600 text-white">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-blue-100">EU AI Act</span>
                      <Badge className="bg-green-600 text-white">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-blue-100">DORA</span>
                      <Badge className="bg-green-600 text-white">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-blue-100">HIPAA</span>
                      <Badge className="bg-blue-600 text-white">Ready</Badge>
                    </div>
                  </div>
                </div>

                <Textarea
                  placeholder="Additional compliance notes or requirements..."
                  className="bg-gray-900 border-gray-700 text-gray-300"
                  rows={4}
                />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-700">
        <Button variant="outline" className="text-white border-gray-600">
          Reset to Defaults
        </Button>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Save className="w-4 h-4 mr-2" />
          Save Configuration
        </Button>
      </div>
    </Card>
  );
};
