
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings, 
  Key, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Eye, 
  EyeOff,
  Mic,
  Volume2,
  Brain,
  Heart,
  Zap,
  Save,
  Server,
  Database,
  Cloud,
  Shield
} from 'lucide-react';

interface APIConfig {
  service: string;
  provider: string;
  apiKey: string;
  endpoint: string;
  status: 'connected' | 'disconnected' | 'testing' | 'error';
  isEnabled: boolean;
  showKey: boolean;
  category: 'ai' | 'backend' | 'infrastructure';
}

export const EnhancedAPIConfiguration = () => {
  const { toast } = useToast();
  const [configs, setConfigs] = useState<{ [key: string]: APIConfig }>({
    // AI Services
    stt: {
      service: 'Speech-to-Text',
      provider: 'whisper',
      apiKey: '',
      endpoint: 'https://api.openai.com/v1/audio/transcriptions',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai'
    },
    tts: {
      service: 'Text-to-Speech',
      provider: 'coqui',
      apiKey: '',
      endpoint: 'http://localhost:5002/api/tts',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai'
    },
    llm: {
      service: 'Language Model',
      provider: 'mistral',
      apiKey: '',
      endpoint: 'https://api.mistral.ai/v1/chat/completions',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai'
    },
    emotion: {
      service: 'Emotion AI',
      provider: 'huggingface',
      apiKey: '',
      endpoint: 'https://api-inference.huggingface.co/models/j-hartmann/emotion-english-distilroberta-base',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai'
    },
    orchestration: {
      service: 'Conversation Orchestration',
      provider: 'rasa',
      apiKey: '',
      endpoint: 'http://localhost:5005/webhooks/rest/webhook',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai'
    },
    // Backend Services
    database: {
      service: 'Database Service',
      provider: 'postgresql',
      apiKey: '',
      endpoint: 'postgresql://localhost:5432/voiceengine',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    redis: {
      service: 'Redis Cache',
      provider: 'redis',
      apiKey: '',
      endpoint: 'redis://localhost:6379',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    webhook: {
      service: 'Webhook Service',
      provider: 'fastapi',
      apiKey: '',
      endpoint: 'http://localhost:8000/webhooks',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    auth: {
      service: 'Authentication Service',
      provider: 'supabase',
      apiKey: '',
      endpoint: 'https://your-project.supabase.co',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    // Infrastructure Services
    monitoring: {
      service: 'Application Monitoring',
      provider: 'datadog',
      apiKey: '',
      endpoint: 'https://api.datadoghq.com',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    },
    logging: {
      service: 'Centralized Logging',
      provider: 'elasticsearch',
      apiKey: '',
      endpoint: 'https://localhost:9200',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    },
    storage: {
      service: 'Object Storage',
      provider: 's3',
      apiKey: '',
      endpoint: 'https://s3.amazonaws.com',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    },
    cdn: {
      service: 'Content Delivery Network',
      provider: 'cloudflare',
      apiKey: '',
      endpoint: 'https://api.cloudflare.com/client/v4',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    }
  });

  const [testResults, setTestResults] = useState<{ [key: string]: string }>({});

  // Load saved configurations on component mount
  useEffect(() => {
    const savedConfigs = localStorage.getItem('apiConfigurations');
    if (savedConfigs) {
      try {
        const parsedConfigs = JSON.parse(savedConfigs);
        setConfigs(prevConfigs => ({
          ...prevConfigs,
          ...parsedConfigs
        }));
        toast({
          title: "Configurations Loaded",
          description: "Previously saved API configurations have been loaded.",
        });
      } catch (error) {
        console.error('Error loading saved configurations:', error);
      }
    }
  }, [toast]);

  const techStackOptions = {
    // AI Services options
    stt: [
      { value: 'whisper', label: 'OpenAI Whisper (Free)', endpoint: 'https://api.openai.com/v1/audio/transcriptions' },
      { value: 'mozilla-deepspeech', label: 'Mozilla DeepSpeech (Free)', endpoint: 'http://localhost:9000/stt' },
      { value: 'wav2vec2', label: 'Facebook wav2vec2 (Free)', endpoint: 'https://api-inference.huggingface.co/models/facebook/wav2vec2-base-960h' },
      { value: 'vosk', label: 'Vosk (Offline Free)', endpoint: 'http://localhost:2700/asr' }
    ],
    tts: [
      { value: 'coqui', label: 'Coqui TTS (Free)', endpoint: 'http://localhost:5002/api/tts' },
      { value: 'bark', label: 'Bark by Suno AI (Free)', endpoint: 'https://api-inference.huggingface.co/models/suno/bark' },
      { value: 'espnet', label: 'ESPnet TTS (Free)', endpoint: 'http://localhost:8080/tts' },
      { value: 'mozilla-tts', label: 'Mozilla TTS (Free)', endpoint: 'http://localhost:5002/api/tts' }
    ],
    llm: [
      { value: 'mistral', label: 'Mistral 7B (Free)', endpoint: 'https://api.mistral.ai/v1/chat/completions' },
      { value: 'llama2', label: 'Llama 2 7B (Free)', endpoint: 'https://api-inference.huggingface.co/models/meta-llama/Llama-2-7b-chat-hf' },
      { value: 'zephyr', label: 'Zephyr 7B Beta (Free)', endpoint: 'https://api-inference.huggingface.co/models/HuggingFaceH4/zephyr-7b-beta' },
      { value: 'code-llama', label: 'Code Llama 7B (Free)', endpoint: 'https://api-inference.huggingface.co/models/codellama/CodeLlama-7b-Instruct-hf' },
      { value: 'openchat', label: 'OpenChat 3.5 (Free)', endpoint: 'https://api-inference.huggingface.co/models/openchat/openchat-3.5-0106' }
    ],
    emotion: [
      { value: 'huggingface', label: 'BERT Emotion (Free)', endpoint: 'https://api-inference.huggingface.co/models/j-hartmann/emotion-english-distilroberta-base' },
      { value: 'wav2vec2-emotion', label: 'wav2vec2 Emotion (Free)', endpoint: 'https://api-inference.huggingface.co/models/facebook/wav2vec2-large-xlsr-53-spanish' },
      { value: 'transformers-emotion', label: 'Transformers.js Emotion (Browser)', endpoint: 'browser-local' },
      { value: 'custom-emotion', label: 'Custom Emotion Model (Free)', endpoint: 'http://localhost:8001/emotion' }
    ],
    orchestration: [
      { value: 'rasa', label: 'Rasa Open Source (Free)', endpoint: 'http://localhost:5005/webhooks/rest/webhook' },
      { value: 'fastapi-custom', label: 'Custom FastAPI (Free)', endpoint: 'http://localhost:8000/chat' },
      { value: 'haystack', label: 'Haystack NLP (Free)', endpoint: 'http://localhost:8001/query' },
      { value: 'chainlit', label: 'Chainlit (Free)', endpoint: 'http://localhost:8002/chat' }
    ],
    // Backend Services options
    database: [
      { value: 'postgresql', label: 'PostgreSQL', endpoint: 'postgresql://localhost:5432/voiceengine' },
      { value: 'mysql', label: 'MySQL', endpoint: 'mysql://localhost:3306/voiceengine' },
      { value: 'mongodb', label: 'MongoDB', endpoint: 'mongodb://localhost:27017/voiceengine' },
      { value: 'supabase', label: 'Supabase', endpoint: 'https://your-project.supabase.co' }
    ],
    redis: [
      { value: 'redis', label: 'Redis', endpoint: 'redis://localhost:6379' },
      { value: 'redis-cloud', label: 'Redis Cloud', endpoint: 'redis://your-instance.redis.cloud:16379' },
      { value: 'elasticache', label: 'AWS ElastiCache', endpoint: 'redis://your-cluster.cache.amazonaws.com:6379' }
    ],
    webhook: [
      { value: 'fastapi', label: 'FastAPI Webhooks', endpoint: 'http://localhost:8000/webhooks' },
      { value: 'express', label: 'Express.js Webhooks', endpoint: 'http://localhost:3000/webhooks' },
      { value: 'flask', label: 'Flask Webhooks', endpoint: 'http://localhost:5000/webhooks' }
    ],
    auth: [
      { value: 'supabase', label: 'Supabase Auth', endpoint: 'https://your-project.supabase.co' },
      { value: 'firebase', label: 'Firebase Auth', endpoint: 'https://your-project.firebaseapp.com' },
      { value: 'auth0', label: 'Auth0', endpoint: 'https://your-domain.auth0.com' }
    ],
    // Infrastructure Services options
    monitoring: [
      { value: 'datadog', label: 'Datadog', endpoint: 'https://api.datadoghq.com' },
      { value: 'newrelic', label: 'New Relic', endpoint: 'https://api.newrelic.com' },
      { value: 'prometheus', label: 'Prometheus', endpoint: 'http://localhost:9090' }
    ],
    logging: [
      { value: 'elasticsearch', label: 'Elasticsearch', endpoint: 'https://localhost:9200' },
      { value: 'splunk', label: 'Splunk', endpoint: 'https://your-instance.splunkcloud.com' },
      { value: 'logstash', label: 'Logstash', endpoint: 'http://localhost:5044' }
    ],
    storage: [
      { value: 's3', label: 'AWS S3', endpoint: 'https://s3.amazonaws.com' },
      { value: 'gcs', label: 'Google Cloud Storage', endpoint: 'https://storage.googleapis.com' },
      { value: 'azure', label: 'Azure Blob Storage', endpoint: 'https://your-account.blob.core.windows.net' }
    ],
    cdn: [
      { value: 'cloudflare', label: 'Cloudflare', endpoint: 'https://api.cloudflare.com/client/v4' },
      { value: 'aws-cloudfront', label: 'AWS CloudFront', endpoint: 'https://cloudfront.amazonaws.com' },
      { value: 'fastly', label: 'Fastly', endpoint: 'https://api.fastly.com' }
    ]
  };

  const updateConfig = (service: string, field: string, value: any) => {
    setConfigs(prev => ({
      ...prev,
      [service]: {
        ...prev[service],
        [field]: value
      }
    }));
  };

  const toggleKeyVisibility = (service: string) => {
    updateConfig(service, 'showKey', !configs[service].showKey);
  };

  const testConnection = async (service: string) => {
    setTestResults(prev => ({ ...prev, [service]: 'testing' }));
    updateConfig(service, 'status', 'testing');

    try {
      // Simulate API test - in production, this would make actual API calls
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock test result based on whether API key is provided
      const success = configs[service].apiKey.length > 0 || configs[service].endpoint.length > 0;
      
      setTestResults(prev => ({ 
        ...prev, 
        [service]: success ? 'success' : 'error' 
      }));
      updateConfig(service, 'status', success ? 'connected' : 'error');
      
      toast({
        title: success ? "Connection Successful" : "Connection Failed",
        description: success 
          ? `${configs[service].service} is now connected and responding.`
          : `Failed to connect to ${configs[service].service}. Please check your configuration.`,
        variant: success ? "default" : "destructive"
      });
    } catch (error) {
      setTestResults(prev => ({ ...prev, [service]: 'error' }));
      updateConfig(service, 'status', 'error');
      toast({
        title: "Connection Error",
        description: `An error occurred while testing ${configs[service].service}.`,
        variant: "destructive"
      });
    }
  };

  const saveConfiguration = () => {
    try {
      localStorage.setItem('apiConfigurations', JSON.stringify(configs));
      toast({
        title: "Configuration Saved",
        description: "All API configurations have been saved successfully.",
      });
      console.log('API configurations saved:', configs);
    } catch (error) {
      console.error('Error saving configurations:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save API configurations. Please try again.",
        variant: "destructive"
      });
    }
  };

  const exportConfiguration = () => {
    try {
      const configExport = {
        timestamp: new Date().toISOString(),
        configurations: configs
      };
      const dataStr = JSON.stringify(configExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `voice-engine-config-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);
      
      toast({
        title: "Configuration Exported",
        description: "Configuration file has been downloaded.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export configuration.",
        variant: "destructive"
      });
    }
  };

  const getServiceIcon = (service: string) => {
    switch (service) {
      case 'stt': return <Mic className="w-5 h-5" />;
      case 'tts': return <Volume2 className="w-5 h-5" />;
      case 'llm': return <Brain className="w-5 h-5" />;
      case 'emotion': return <Heart className="w-5 h-5" />;
      case 'orchestration': return <Zap className="w-5 h-5" />;
      case 'database': return <Database className="w-5 h-5" />;
      case 'redis': return <Server className="w-5 h-5" />;
      case 'webhook': return <Zap className="w-5 h-5" />;
      case 'auth': return <Shield className="w-5 h-5" />;
      case 'monitoring': return <Settings className="w-5 h-5" />;
      case 'logging': return <Settings className="w-5 h-5" />;
      case 'storage': return <Cloud className="w-5 h-5" />;
      case 'cdn': return <Cloud className="w-5 h-5" />;
      default: return <Settings className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-400 border-green-400';
      case 'testing': return 'text-yellow-400 border-yellow-400';
      case 'error': return 'text-red-400 border-red-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const getCategoryConfigs = (category: 'ai' | 'backend' | 'infrastructure') => {
    return Object.entries(configs).filter(([_, config]) => config.category === category);
  };

  const renderConfigurationCard = (service: string, config: APIConfig) => (
    <Card key={service} className="p-4 bg-gray-900/50 border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-blue-400">
            {getServiceIcon(service)}
          </div>
          <div>
            <h4 className="text-white font-medium">{config.service}</h4>
            <p className="text-gray-400 text-sm">Provider: {config.provider}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Switch 
            checked={config.isEnabled}
            onCheckedChange={(checked) => updateConfig(service, 'isEnabled', checked)}
          />
          <Badge variant="outline" className={getStatusColor(config.status)}>
            {config.status}
          </Badge>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <Label className="text-gray-300">Provider</Label>
          <Select 
            value={config.provider}
            onValueChange={(value) => {
              const option = techStackOptions[service as keyof typeof techStackOptions]?.find(opt => opt.value === value);
              updateConfig(service, 'provider', value);
              if (option) {
                updateConfig(service, 'endpoint', option.endpoint);
              }
            }}
          >
            <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              {techStackOptions[service as keyof typeof techStackOptions]?.map((option) => (
                <SelectItem key={option.value} value={option.value} className="text-white hover:bg-gray-700">
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label className="text-gray-300">API Endpoint</Label>
          <Input
            value={config.endpoint}
            onChange={(e) => updateConfig(service, 'endpoint', e.target.value)}
            placeholder="API Endpoint URL"
            className="bg-gray-800 border-gray-700 text-white"
          />
        </div>

        <div>
          <Label className="text-gray-300">API Key / Connection String</Label>
          <div className="flex space-x-2">
            <Input
              type={config.showKey ? 'text' : 'password'}
              value={config.apiKey}
              onChange={(e) => updateConfig(service, 'apiKey', e.target.value)}
              placeholder="Enter API Key or Connection String"
              className="bg-gray-800 border-gray-700 text-white"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleKeyVisibility(service)}
              className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700"
            >
              {config.showKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        <div className="flex items-end">
          <Button
            onClick={() => testConnection(service)}
            variant="outline"
            size="sm"
            disabled={!config.isEnabled || testResults[service] === 'testing'}
            className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 w-full"
          >
            <TestTube className="w-4 h-4 mr-2" />
            {testResults[service] === 'testing' ? 'Testing...' : 'Test Connection'}
          </Button>
        </div>
      </div>

      {testResults[service] && (
        <div className={`mt-3 p-3 rounded border ${
          testResults[service] === 'success' 
            ? 'bg-green-500/10 border-green-500/30 text-green-300'
            : testResults[service] === 'error'
            ? 'bg-red-500/10 border-red-500/30 text-red-300'
            : 'bg-yellow-500/10 border-yellow-500/30 text-yellow-300'
        }`}>
          {testResults[service] === 'success' && (
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 mr-2" />
              Connection successful - Service responding correctly
            </div>
          )}
          {testResults[service] === 'error' && (
            <div className="flex items-center">
              <AlertCircle className="w-4 h-4 mr-2" />
              Connection failed - Check API key and endpoint
            </div>
          )}
          {testResults[service] === 'testing' && (
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 animate-pulse" />
              Testing connection...
            </div>
          )}
        </div>
      )}
    </Card>
  );

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-semibold text-white">AI Voice Engine API Configuration</h3>
            <Badge variant="outline" className="text-blue-400 border-blue-400">
              Enterprise Stack
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              onClick={exportConfiguration}
              variant="outline"
              className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700"
            >
              <Key className="w-4 h-4 mr-2" />
              Export Config
            </Button>
            <Button 
              onClick={saveConfiguration}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              Save All Configurations
            </Button>
          </div>
        </div>

        <Tabs defaultValue="ai" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-900/50 border-gray-700">
            <TabsTrigger value="ai" className="text-white data-[state=active]:bg-blue-600">AI Services</TabsTrigger>
            <TabsTrigger value="backend" className="text-white data-[state=active]:bg-blue-600">Backend Services</TabsTrigger>
            <TabsTrigger value="infrastructure" className="text-white data-[state=active]:bg-blue-600">Infrastructure</TabsTrigger>
          </TabsList>

          <TabsContent value="ai" className="space-y-4">
            <div className="grid gap-6">
              {getCategoryConfigs('ai').map(([service, config]) => 
                renderConfigurationCard(service, config)
              )}
            </div>
          </TabsContent>

          <TabsContent value="backend" className="space-y-4">
            <div className="grid gap-6">
              {getCategoryConfigs('backend').map(([service, config]) => 
                renderConfigurationCard(service, config)
              )}
            </div>
          </TabsContent>

          <TabsContent value="infrastructure" className="space-y-4">
            <div className="grid gap-6">
              {getCategoryConfigs('infrastructure').map(([service, config]) => 
                renderConfigurationCard(service, config)
              )}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};
