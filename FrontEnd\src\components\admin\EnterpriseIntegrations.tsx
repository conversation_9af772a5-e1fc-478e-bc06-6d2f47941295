
import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Globe } from 'lucide-react';
import { Integration } from '@/types/integrations';
import { initialIntegrations } from '@/data/integrationData';
import { 
  testCRMConnection, 
  testTelephonyConnection, 
  testSupportConnection, 
  testAnalyticsConnection, 
  testKnowledgeConnection, 
  testAIConnection, 
  testSecurityConnection 
} from '@/services/integrationTestService';
import { IntegrationsOverview } from './integrations/IntegrationsOverview';
import { IntegrationConfiguration } from './integrations/IntegrationConfiguration';
import { IntegrationsMonitoring } from './integrations/IntegrationsMonitoring';
import { WebhooksConfiguration } from './integrations/WebhooksConfiguration';

export const EnterpriseIntegrations = () => {
  const { toast } = useToast();
  const [integrations, setIntegrations] = useState<{ [key: string]: Integration }>(initialIntegrations);
  const [selectedIntegration, setSelectedIntegration] = useState<string>('salesforce');
  const [testResults, setTestResults] = useState<{ [key: string]: 'success' | 'error' | 'testing' }>({});
  const [connectionLogs, setConnectionLogs] = useState<{ [key: string]: string[] }>({});
  const [realTimeSync, setRealTimeSync] = useState<{ [key: string]: boolean }>({});

  // Real-time sync status updates
  useEffect(() => {
    const interval = setInterval(() => {
      Object.keys(integrations).forEach(id => {
        if (integrations[id].status === 'connected' && realTimeSync[id]) {
          // Simulate real-time data updates
          const now = new Date();
          setIntegrations(prev => ({
            ...prev,
            [id]: {
              ...prev[id],
              lastSync: now
            }
          }));
        }
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [integrations, realTimeSync]);

  const updateIntegrationConfig = (integrationId: string, field: string, value: any) => {
    setIntegrations(prev => ({
      ...prev,
      [integrationId]: {
        ...prev[integrationId],
        config: {
          ...prev[integrationId].config,
          [field]: value
        }
      }
    }));
  };

  const testIntegration = async (integrationId: string) => {
    const integration = integrations[integrationId];
    setTestResults(prev => ({ ...prev, [integrationId]: 'testing' }));
    setConnectionLogs(prev => ({
      ...prev,
      [integrationId]: [`Testing connection to ${integration.name}...`]
    }));
    
    try {
      // Real API testing based on integration type
      let testResult = false;
      const logs: string[] = [];

      switch (integration.category) {
        case 'crm':
          testResult = await testCRMConnection(integration, logs);
          break;
        case 'telephony':
          testResult = await testTelephonyConnection(integration, logs);
          break;
        case 'support':
          testResult = await testSupportConnection(integration, logs);
          break;
        case 'analytics':
          testResult = await testAnalyticsConnection(integration, logs);
          break;
        case 'knowledge':
          testResult = await testKnowledgeConnection(integration, logs);
          break;
        case 'ai_enhancement':
          testResult = await testAIConnection(integration, logs);
          break;
        case 'security':
          testResult = await testSecurityConnection(integration, logs);
          break;
        default:
          testResult = false;
      }

      setConnectionLogs(prev => ({
        ...prev,
        [integrationId]: [...(prev[integrationId] || []), ...logs]
      }));

      setTestResults(prev => ({ 
        ...prev, 
        [integrationId]: testResult ? 'success' : 'error' 
      }));

      if (testResult) {
        setIntegrations(prev => ({
          ...prev,
          [integrationId]: {
            ...prev[integrationId],
            status: 'connected',
            lastSync: new Date()
          }
        }));
        setRealTimeSync(prev => ({ ...prev, [integrationId]: true }));
        toast({
          title: "Integration Connected",
          description: `${integration.name} has been successfully connected and is now syncing in real-time.`
        });
      } else {
        toast({
          title: "Connection Failed",
          description: `Failed to connect to ${integration.name}. Please check your configuration.`,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Integration test error:', error);
      setTestResults(prev => ({ ...prev, [integrationId]: 'error' }));
      toast({
        title: "Test Error",
        description: `An error occurred while testing ${integration.name}.`,
        variant: "destructive"
      });
    }
  };

  const saveIntegration = (integrationId: string) => {
    const integration = integrations[integrationId];
    
    // Validate required fields
    const missingFields = integration.requiredFields.filter(field => 
      !integration.config[field] || integration.config[field].trim() === ''
    );

    if (missingFields.length > 0) {
      toast({
        title: "Missing Configuration",
        description: `Please fill in required fields: ${missingFields.join(', ')}`,
        variant: "destructive"
      });
      return;
    }

    setIntegrations(prev => ({
      ...prev,
      [integrationId]: {
        ...prev[integrationId],
        status: 'configuring'
      }
    }));

    // Save to localStorage (in production, this would be saved to backend)
    localStorage.setItem('enterpriseIntegrations', JSON.stringify(integrations));
    
    toast({
      title: "Configuration Saved",
      description: `${integration.name} configuration has been saved successfully.`
    });
  };

  const groupedIntegrations = Object.values(integrations).reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = [];
    }
    acc[integration.category].push(integration);
    return acc;
  }, {} as { [key: string]: Integration[] });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Globe className="w-6 h-6 mr-2" />
          Enterprise Integrations
        </h2>
        <div className="flex items-center space-x-4">
          <Badge variant="outline" className="text-green-400 border-green-400">
            {Object.values(integrations).filter(i => i.status === 'connected').length} Active
          </Badge>
          <Badge variant="outline" className="text-blue-400 border-blue-400">
            {Object.values(integrations).filter(i => realTimeSync[i.id]).length} Real-time Sync
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-900/50">
          <TabsTrigger value="overview" className="text-white data-[state=active]:bg-blue-600">Overview</TabsTrigger>
          <TabsTrigger value="configure" className="text-white data-[state=active]:bg-blue-600">Configure</TabsTrigger>
          <TabsTrigger value="monitoring" className="text-white data-[state=active]:bg-blue-600">Monitoring</TabsTrigger>
          <TabsTrigger value="webhooks" className="text-white data-[state=active]:bg-blue-600">Webhooks & Events</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <IntegrationsOverview 
            groupedIntegrations={groupedIntegrations}
            realTimeSync={realTimeSync}
            setSelectedIntegration={setSelectedIntegration}
          />
        </TabsContent>

        <TabsContent value="configure" className="space-y-6">
          <IntegrationConfiguration
            groupedIntegrations={groupedIntegrations}
            selectedIntegration={selectedIntegration}
            integrations={integrations}
            testResults={testResults}
            connectionLogs={connectionLogs}
            realTimeSync={realTimeSync}
            setSelectedIntegration={setSelectedIntegration}
            updateIntegrationConfig={updateIntegrationConfig}
            testIntegration={testIntegration}
            saveIntegration={saveIntegration}
            setRealTimeSync={setRealTimeSync}
          />
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-6">
          <IntegrationsMonitoring integrations={integrations} />
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <WebhooksConfiguration />
        </TabsContent>
      </Tabs>
    </div>
  );
};
