
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  BarChart3, 
  FileText, 
  TrendingUp, 
  Shield, 
  Settings,
  Users,
  Database,
  Activity
} from 'lucide-react';

interface NavigationTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  userRole: 'super_admin' | 'admin' | 'viewer';
}

export const NavigationTabs = ({ activeTab, onTabChange, userRole }: NavigationTabsProps) => {
  const tabs = [
    { id: 'overview', label: 'Overview', icon: <BarChart3 className="w-4 h-4" />, access: ['super_admin', 'admin', 'viewer'] },
    { id: 'logs', label: 'Call Logs', icon: <FileText className="w-4 h-4" />, access: ['super_admin', 'admin', 'viewer'] },
    { id: 'metrics', label: 'Analytics', icon: <TrendingUp className="w-4 h-4" />, access: ['super_admin', 'admin'] },
    { id: 'compliance', label: 'Compliance', icon: <Shield className="w-4 h-4" />, access: ['super_admin', 'admin'] },
    { id: 'settings', label: 'Settings', icon: <Settings className="w-4 h-4" />, access: ['super_admin', 'admin'] }
  ];

  const accessibleTabs = tabs.filter(tab => tab.access.includes(userRole));

  return (
    <div className="mb-8 bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-2">
      <div className="flex space-x-2">
        {accessibleTabs.map((tab) => (
          <Button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            variant={activeTab === tab.id ? "default" : "ghost"}
            className={`flex items-center space-x-2 ${
              activeTab === tab.id 
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white' 
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
          >
            {tab.icon}
            <span>{tab.label}</span>
          </Button>
        ))}
      </div>
    </div>
  );
};
