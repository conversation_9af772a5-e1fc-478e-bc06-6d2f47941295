
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Shield, 
  Database, 
  Zap, 
  Eye, 
  Lock,
  Filter,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

export const RealTimeDataProcessor = () => {
  const [processingData, setProcessingData] = useState([]);
  const [privacyLayers, setPrivacyLayers] = useState([]);

  useEffect(() => {
    // Simulate real-time data processing
    const interval = setInterval(() => {
      const newProcess = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        layer: Math.floor(Math.random() * 5) + 1,
        dataType: ['voice', 'text', 'metadata', 'emotion', 'intent'][Math.floor(Math.random() * 5)],
        status: 'processing',
        privacyStatus: 'tokenized',
        processingTime: Math.random() * 100 + 50
      };
      
      setProcessingData(prev => [newProcess, ...prev.slice(0, 9)]);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const dataLayers = [
    {
      layer: 1,
      name: 'Audio Capture',
      description: 'Raw audio input with immediate encryption',
      privacy: 'AES-256 encryption applied',
      status: 'active',
      icon: <Activity className="w-4 h-4" />
    },
    {
      layer: 2,
      name: 'Speech Processing',
      description: 'Whisper STT with data tokenization',
      privacy: 'Personal identifiers replaced with tokens',
      status: 'active',
      icon: <Zap className="w-4 h-4" />
    },
    {
      layer: 3,
      name: 'Intent Recognition',
      description: 'Mistral AI analysis with privacy masking',
      privacy: 'Sensitive data masked before processing',
      status: 'active',
      icon: <Eye className="w-4 h-4" />
    },
    {
      layer: 4,
      name: 'Response Generation',
      description: 'Context-aware response with data minimization',
      privacy: 'Only necessary context retained',
      status: 'active',
      icon: <Database className="w-4 h-4" />
    },
    {
      layer: 5,
      name: 'Audio Synthesis',
      description: 'Coqui TTS with secure delivery',
      privacy: 'End-to-end encrypted transmission',
      status: 'active',
      icon: <Shield className="w-4 h-4" />
    }
  ];

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Activity className="w-6 h-6 text-green-400" />
            <h3 className="text-xl font-semibold text-white">Real-Time Data Processing Pipeline</h3>
            <Badge variant="outline" className="text-green-400 border-green-400">
              Live Processing
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="text-white border-gray-600 hover:bg-gray-800">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm" className="text-white border-gray-600 hover:bg-gray-800">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="text-white border-gray-600 hover:bg-gray-800">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Processing Layers */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {dataLayers.map((layer) => (
            <div key={layer.layer} className="p-4 bg-gray-900/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="text-blue-400">{layer.icon}</div>
                  <span className="text-white font-medium">Layer {layer.layer}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-400">Active</span>
                </div>
              </div>
              
              <h4 className="text-white text-sm font-medium mb-1">{layer.name}</h4>
              <p className="text-gray-400 text-xs mb-3">{layer.description}</p>
              
              <div className="flex items-center space-x-1 p-2 bg-green-500/10 rounded border border-green-500/20">
                <Lock className="w-3 h-3 text-green-400" />
                <span className="text-xs text-green-300">{layer.privacy}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Real-time Processing Log */}
        <div className="space-y-3">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <Clock className="w-4 h-4 text-purple-400" />
            <span>Live Processing Events</span>
          </h4>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {processingData.map((process) => (
              <div key={process.id} className="flex items-center justify-between p-3 bg-gray-900/30 rounded border border-gray-700">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                  <div>
                    <span className="text-white text-sm">Layer {process.layer} - {process.dataType}</span>
                    <div className="text-xs text-gray-400">{new Date(process.timestamp).toLocaleTimeString()}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-green-400 border-green-400 text-xs">
                    Privacy Protected
                  </Badge>
                  <span className="text-xs text-gray-400">{Math.round(process.processingTime)}ms</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Privacy & Trust Metrics */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
          <Shield className="w-6 h-6 text-green-400" />
          <span>Privacy & Trust Metrics</span>
        </h3>
        
        <div className="grid md:grid-cols-4 gap-4">
          <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-4 h-4 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Data Tokenization</span>
            </div>
            <p className="text-2xl font-bold text-white">100%</p>
            <p className="text-xs text-gray-400">Personal data tokenized</p>
          </div>
          
          <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <Lock className="w-4 h-4 text-blue-400" />
              <span className="text-blue-400 text-sm font-medium">Encryption</span>
            </div>
            <p className="text-2xl font-bold text-white">AES-256</p>
            <p className="text-xs text-gray-400">End-to-end encrypted</p>
          </div>
          
          <div className="p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <Database className="w-4 h-4 text-purple-400" />
              <span className="text-purple-400 text-sm font-medium">Data Retention</span>
            </div>
            <p className="text-2xl font-bold text-white">30d</p>
            <p className="text-xs text-gray-400">Auto-deletion policy</p>
          </div>
          
          <div className="p-4 bg-orange-500/10 rounded-lg border border-orange-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="w-4 h-4 text-orange-400" />
              <span className="text-orange-400 text-sm font-medium">Compliance Score</span>
            </div>
            <p className="text-2xl font-bold text-white">98.5%</p>
            <p className="text-xs text-gray-400">GDPR + EU AI Act</p>
          </div>
        </div>
      </Card>
    </div>
  );
};
