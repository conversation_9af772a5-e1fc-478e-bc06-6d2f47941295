
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Phone, Clock, Users, Activity, TrendingUp, AlertCircle, CheckCircle, Zap } from 'lucide-react';

export const RealTimeStatsCards = () => {
  const [stats, setStats] = useState({
    totalCalls: 1247,
    avgResponseTime: 2.3,
    successRate: 94.2,
    activeUsers: 89,
    aiConfidence: 96.8,
    errorRate: 2.1,
    voiceGenderUsage: { male: 45, female: 55 },
    topLanguages: [
      { lang: 'EN', percentage: 45 },
      { lang: 'DE', percentage: 25 },
      { lang: 'FR', percentage: 15 },
      { lang: 'ES', percentage: 10 },
      { lang: 'IT', percentage: 5 }
    ]
  });

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalCalls: prev.totalCalls + Math.floor(Math.random() * 5),
        avgResponseTime: Math.max(1.5, Math.min(4.0, prev.avgResponseTime + (Math.random() - 0.5) * 0.1)),
        successRate: Math.max(85, Math.min(99, prev.successRate + (Math.random() - 0.5) * 2)),
        activeUsers: Math.max(50, Math.min(150, prev.activeUsers + Math.floor((Math.random() - 0.5) * 10))),
        aiConfidence: Math.max(90, Math.min(99, prev.aiConfidence + (Math.random() - 0.5) * 2)),
        errorRate: Math.max(0.5, Math.min(5, prev.errorRate + (Math.random() - 0.5) * 0.5))
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const mainStats = [
    {
      title: 'Total Calls Today',
      value: stats.totalCalls.toLocaleString(),
      icon: <Phone className="w-6 h-6" />,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      trend: '+12%'
    },
    {
      title: 'Avg Response Time',
      value: `${stats.avgResponseTime.toFixed(1)}s`,
      icon: <Clock className="w-6 h-6" />,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      trend: '-5%'
    },
    {
      title: 'Success Rate',
      value: `${stats.successRate.toFixed(1)}%`,
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      trend: '+2%'
    },
    {
      title: 'Active Users',
      value: stats.activeUsers.toString(),
      icon: <Users className="w-6 h-6" />,
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/10',
      trend: '+8%'
    },
    {
      title: 'AI Confidence',
      value: `${stats.aiConfidence.toFixed(1)}%`,
      icon: <Activity className="w-6 h-6" />,
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      trend: '+1%'
    },
    {
      title: 'Error Rate',
      value: `${stats.errorRate.toFixed(1)}%`,
      icon: <AlertCircle className="w-6 h-6" />,
      color: 'text-red-400',
      bgColor: 'bg-red-500/10',
      trend: '-3%'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mainStats.map((stat, index) => (
          <Card key={index} className="p-6 bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{stat.title}</p>
                <p className="text-3xl font-bold text-white mt-2">{stat.value}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-sm text-green-400">{stat.trend}</span>
                </div>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <div className={stat.color}>
                  {stat.icon}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Voice Gender Usage & Top Languages */}
      <div className="grid lg:grid-cols-2 gap-6">
        <Card className="p-6 bg-gray-800/50 border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">Voice Gender Usage</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Male</span>
              <span className="text-white font-medium">{stats.voiceGenderUsage.male}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${stats.voiceGenderUsage.male}%` }}
              ></div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Female</span>
              <span className="text-white font-medium">{stats.voiceGenderUsage.female}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-pink-500 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${stats.voiceGenderUsage.female}%` }}
              ></div>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-gray-800/50 border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">Top Languages</h4>
          <div className="space-y-3">
            {stats.topLanguages.map((lang, idx) => (
              <div key={idx} className="flex justify-between items-center">
                <span className="text-gray-300">{lang.lang}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${lang.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-white font-medium w-8">{lang.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};
