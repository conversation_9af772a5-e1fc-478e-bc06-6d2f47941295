
import React from 'react';
import { Card } from '@/components/ui/card';
import { Phone, Clock, Users, Activity } from 'lucide-react';

export const StatsCards = () => {
  const stats = [
    {
      title: 'Total Calls Today',
      value: '1,247',
      icon: <Phone className="w-6 h-6" />,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10'
    },
    {
      title: 'Avg Response Time',
      value: '2.3s',
      icon: <Clock className="w-6 h-6" />,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10'
    },
    {
      title: 'Success Rate',
      value: '94.2%',
      icon: <Activity className="w-6 h-6" />,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10'
    },
    {
      title: 'Active Users',
      value: '89',
      icon: <Users className="w-6 h-6" />,
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/10'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <Card key={index} className="p-6 bg-gray-800/50 border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">{stat.title}</p>
              <p className="text-3xl font-bold text-white mt-2">{stat.value}</p>
            </div>
            <div className={`p-3 rounded-lg ${stat.bgColor}`}>
              <div className={stat.color}>
                {stat.icon}
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
