
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  UserPlus, 
  Users, 
  Shield, 
  Edit, 
  Trash2, 
  Crown,
  Key,
  Mail,
  Calendar
} from 'lucide-react';

interface User {
  id: string;
  email: string;
  role: 'super_admin' | 'admin' | 'viewer';
  createdAt: string;
  lastLogin: string;
  status: 'active' | 'inactive';
}

export const UserManagement = () => {
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      email: '<EMAIL>',
      role: 'super_admin',
      createdAt: '2024-01-01',
      lastLogin: new Date().toISOString(),
      status: 'active'
    },
    {
      id: '2',
      email: '<EMAIL>',
      role: 'admin',
      createdAt: '2024-01-15',
      lastLogin: '2024-01-16T10:30:00Z',
      status: 'active'
    },
    {
      id: '3',
      email: '<EMAIL>',
      role: 'viewer',
      createdAt: '2024-01-16',
      lastLogin: '2024-01-16T14:20:00Z',
      status: 'active'
    }
  ]);

  const [newUser, setNewUser] = useState({
    email: '',
    role: 'viewer' as const,
    password: ''
  });

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin': return <Crown className="w-4 h-4 text-yellow-400" />;
      case 'admin': return <Shield className="w-4 h-4 text-blue-400" />;
      case 'viewer': return <Users className="w-4 h-4 text-green-400" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin': return 'text-yellow-400 border-yellow-400';
      case 'admin': return 'text-blue-400 border-blue-400';
      case 'viewer': return 'text-green-400 border-green-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const addUser = () => {
    if (!newUser.email || !newUser.password) return;
    
    const user: User = {
      id: Date.now().toString(),
      email: newUser.email,
      role: newUser.role,
      createdAt: new Date().toISOString(),
      lastLogin: 'Never',
      status: 'active'
    };
    
    setUsers([...users, user]);
    setNewUser({ email: '', role: 'viewer', password: '' });
  };

  const removeUser = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId));
  };

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Users className="w-6 h-6 text-blue-400" />
          <h3 className="text-xl font-semibold text-white">User Management</h3>
          <Badge variant="outline" className="text-green-400 border-green-400">
            Super Admin Only
          </Badge>
        </div>
      </div>

      {/* Add New User */}
      <div className="mb-6 p-4 bg-gray-900/50 rounded-lg border border-gray-700">
        <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
          <UserPlus className="w-4 h-4" />
          <span>Add New User</span>
        </h4>
        
        <div className="grid md:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="email" className="text-gray-300">Email</Label>
            <Input
              id="email"
              type="email"
              value={newUser.email}
              onChange={(e) => setNewUser({...newUser, email: e.target.value})}
              placeholder="<EMAIL>"
              className="bg-gray-900 border-gray-700 text-gray-300"
            />
          </div>
          <div>
            <Label htmlFor="password" className="text-gray-300">Password</Label>
            <Input
              id="password"
              type="password"
              value={newUser.password}
              onChange={(e) => setNewUser({...newUser, password: e.target.value})}
              placeholder="Secure password"
              className="bg-gray-900 border-gray-700 text-gray-300"
            />
          </div>
          <div>
            <Label htmlFor="role" className="text-gray-300">Role</Label>
            <select
              id="role"
              value={newUser.role}
              onChange={(e) => setNewUser({...newUser, role: e.target.value as any})}
              className="w-full px-3 py-2 bg-gray-900 border border-gray-700 rounded text-gray-300"
            >
              <option value="viewer">Viewer</option>
              <option value="admin">Admin</option>
            </select>
          </div>
          <div className="flex items-end">
            <Button onClick={addUser} className="w-full bg-blue-600 hover:bg-blue-700">
              <UserPlus className="w-4 h-4 mr-2" />
              Add User
            </Button>
          </div>
        </div>
      </div>

      {/* Users List */}
      <div className="space-y-3">
        <h4 className="text-white font-medium">Current Users</h4>
        
        {users.map((user) => (
          <div key={user.id} className="flex items-center justify-between p-4 bg-gray-900/30 rounded-lg border border-gray-700">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                {getRoleIcon(user.role)}
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-white font-medium">{user.email}</span>
                    {user.role === 'super_admin' && (
                      <Badge variant="outline" className="text-yellow-400 border-yellow-400 text-xs">
                        You
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 text-xs text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>Created: {new Date(user.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Key className="w-3 h-3" />
                      <span>Last login: {user.lastLogin === 'Never' ? 'Never' : new Date(user.lastLogin).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className={`${getRoleColor(user.role)} text-xs`}>
                {user.role.replace('_', ' ').toUpperCase()}
              </Badge>
              
              <div className="flex items-center space-x-1">
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <Edit className="w-3 h-3" />
                </Button>
                {user.role !== 'super_admin' && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-red-400 hover:text-red-300"
                    onClick={() => removeUser(user.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Role Permissions */}
      <div className="mt-6 p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
        <h4 className="text-blue-400 font-medium mb-3">Role Permissions</h4>
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Crown className="w-4 h-4 text-yellow-400" />
              <span className="text-yellow-400 font-medium">Super Admin</span>
            </div>
            <ul className="text-gray-300 space-y-1 text-xs">
              <li>• Full system access</li>
              <li>• User management</li>
              <li>• System configuration</li>
              <li>• Compliance management</li>
            </ul>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-4 h-4 text-blue-400" />
              <span className="text-blue-400 font-medium">Admin</span>
            </div>
            <ul className="text-gray-300 space-y-1 text-xs">
              <li>• Dashboard access</li>
              <li>• Call logs & analytics</li>
              <li>• Export capabilities</li>
              <li>• Basic configuration</li>
            </ul>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-4 h-4 text-green-400" />
              <span className="text-green-400 font-medium">Viewer</span>
            </div>
            <ul className="text-gray-300 space-y-1 text-xs">
              <li>• Read-only dashboard</li>
              <li>• View call logs</li>
              <li>• Basic analytics</li>
              <li>• No configuration access</li>
            </ul>
          </div>
        </div>
      </div>
    </Card>
  );
};
