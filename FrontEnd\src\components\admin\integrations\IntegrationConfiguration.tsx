
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { TestTube, Key, ExternalLink, Webhook, AlertCircle, CheckCircle } from 'lucide-react';
import { Integration } from '@/types/integrations';
import { categoryNames } from '@/types/integrations';
import { getCategoryIcon, getStatusIcon } from '@/utils/integrationIcons';

interface IntegrationConfigurationProps {
  groupedIntegrations: { [key: string]: Integration[] };
  selectedIntegration: string;
  integrations: { [key: string]: Integration };
  testResults: { [key: string]: 'success' | 'error' | 'testing' };
  connectionLogs: { [key: string]: string[] };
  realTimeSync: { [key: string]: boolean };
  setSelectedIntegration: (id: string) => void;
  updateIntegrationConfig: (integrationId: string, field: string, value: any) => void;
  testIntegration: (integrationId: string) => void;
  saveIntegration: (integrationId: string) => void;
  setRealTimeSync: React.Dispatch<React.SetStateAction<{ [key: string]: boolean }>>;
}

export const IntegrationConfiguration: React.FC<IntegrationConfigurationProps> = ({
  groupedIntegrations,
  selectedIntegration,
  integrations,
  testResults,
  connectionLogs,
  realTimeSync,
  setSelectedIntegration,
  updateIntegrationConfig,
  testIntegration,
  saveIntegration,
  setRealTimeSync
}) => {
  const currentIntegration = integrations[selectedIntegration];

  return (
    <div className="grid lg:grid-cols-3 gap-6">
      {/* Integration Selection */}
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Select Integration</h3>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {Object.entries(groupedIntegrations).map(([category, categoryIntegrations]) => (
            <div key={category} className="space-y-1">
              <div className="text-sm font-medium text-gray-400 px-2 py-1 flex items-center">
                {getCategoryIcon(category as any)}
                <span className="ml-2">
                  {categoryNames[category as keyof typeof categoryNames] || category}
                </span>
              </div>
              {categoryIntegrations.map((integration) => (
                <Button
                  key={integration.id}
                  onClick={() => setSelectedIntegration(integration.id)}
                  variant={selectedIntegration === integration.id ? "default" : "ghost"}
                  className={`w-full justify-start text-left p-3 h-auto ${
                    selectedIntegration === integration.id 
                      ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  <div className="flex items-center space-x-3 w-full">
                    {getCategoryIcon(integration.category)}
                    <div className="flex-1">
                      <div className="text-sm font-medium">{integration.name}</div>
                      <div className="text-xs opacity-70 capitalize">{integration.status}</div>
                    </div>
                    {getStatusIcon(integration.status)}
                  </div>
                </Button>
              ))}
            </div>
          ))}
        </div>
      </Card>

      {/* Configuration Form */}
      <div className="lg:col-span-2">
        <Card className="p-6 bg-gray-800/50 border-gray-700">
          {currentIntegration ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getCategoryIcon(currentIntegration.category)}
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      Configure {currentIntegration.name}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {categoryNames[currentIntegration.category]} Integration
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => testIntegration(selectedIntegration)}
                    variant="outline"
                    size="sm"
                    disabled={testResults[selectedIntegration] === 'testing'}
                    className="text-white border-gray-600 bg-gray-700/50 hover:bg-gray-600 hover:text-white"
                  >
                    <TestTube className="w-4 h-4 mr-2" />
                    {testResults[selectedIntegration] === 'testing' ? 'Testing...' : 'Test Connection'}
                  </Button>
                  <Button
                    onClick={() => saveIntegration(selectedIntegration)}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Key className="w-4 h-4 mr-2" />
                    Save Configuration
                  </Button>
                </div>
              </div>

              {/* Configuration Fields */}
              <div className="grid gap-4">
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
                  <div className="flex items-center space-x-2 text-blue-300 text-sm">
                    <AlertCircle className="w-4 h-4" />
                    <span>Required fields are marked with an asterisk (*)</span>
                  </div>
                </div>

                {currentIntegration.requiredFields.map((field) => (
                  <div key={field}>
                    <Label className="text-gray-300 capitalize flex items-center">
                      {field.replace(/([A-Z])/g, ' $1').toLowerCase().replace(/_/g, ' ')}
                      <span className="text-red-400 ml-1">*</span>
                      {!currentIntegration.config[field] && (
                        <AlertCircle className="w-4 h-4 ml-2 text-red-400" />
                      )}
                    </Label>
                    {field === 'model' || field === 'domain' || field === 'protocol' ? (
                      <Select
                        value={currentIntegration.config[field] || ''}
                        onValueChange={(value) => updateIntegrationConfig(selectedIntegration, field, value)}
                      >
                        <SelectTrigger className="bg-gray-900 border-gray-700 text-white">
                          <SelectValue placeholder={`Select ${field}`} />
                        </SelectTrigger>
                        <SelectContent className="bg-gray-900 border-gray-700">
                          {field === 'model' && selectedIntegration === 'openai' && (
                            <>
                              <SelectItem value="gpt-4">GPT-4</SelectItem>
                              <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                            </>
                          )}
                          {field === 'domain' && selectedIntegration === 'zoho' && (
                            <>
                              <SelectItem value="com">zoho.com</SelectItem>
                              <SelectItem value="eu">zoho.eu</SelectItem>
                              <SelectItem value="in">zoho.in</SelectItem>
                            </>
                          )}
                          {field === 'protocol' && (
                            <>
                              <SelectItem value="http">HTTP</SelectItem>
                              <SelectItem value="https">HTTPS</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    ) : (
                      <Input
                        type={field.includes('password') || field.includes('token') || field.includes('secret') ? 'password' : 'text'}
                        value={currentIntegration.config[field] || ''}
                        onChange={(e) => updateIntegrationConfig(selectedIntegration, field, e.target.value)}
                        placeholder={`Enter ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`}
                        className={`bg-gray-900 border-gray-700 text-white ${
                          !currentIntegration.config[field] ? 'border-red-500/50' : ''
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>

              {/* Integration Info */}
              <div className="grid md:grid-cols-2 gap-4">
                {currentIntegration.apiEndpoint && (
                  <div className="p-3 bg-blue-500/10 rounded border border-blue-500/30">
                    <div className="flex items-center space-x-2 text-blue-300">
                      <ExternalLink className="w-4 h-4" />
                      <span className="text-sm">API: {currentIntegration.apiEndpoint}</span>
                    </div>
                  </div>
                )}

                {currentIntegration.webhookSupport && (
                  <div className="p-3 bg-green-500/10 rounded border border-green-500/30">
                    <div className="flex items-center space-x-2 text-green-300">
                      <Webhook className="w-4 h-4" />
                      <span className="text-sm">Webhook Support Available</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Test Results */}
              {testResults[selectedIntegration] && (
                <div className={`p-4 rounded border ${
                  testResults[selectedIntegration] === 'success' 
                    ? 'bg-green-500/10 border-green-500/30 text-green-300'
                    : testResults[selectedIntegration] === 'error'
                    ? 'bg-red-500/10 border-red-500/30 text-red-300'
                    : 'bg-yellow-500/10 border-yellow-500/30 text-yellow-300'
                }`}>
                  <div className="font-medium mb-2 flex items-center">
                    {testResults[selectedIntegration] === 'success' && (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Integration Test Successful
                      </>
                    )}
                    {testResults[selectedIntegration] === 'error' && (
                      <>
                        <AlertCircle className="w-4 h-4 mr-2" />
                        Integration Test Failed
                      </>
                    )}
                    {testResults[selectedIntegration] === 'testing' && (
                      <>
                        <TestTube className="w-4 h-4 mr-2 animate-spin" />
                        Testing Integration...
                      </>
                    )}
                  </div>
                  {connectionLogs[selectedIntegration] && (
                    <div className="space-y-1 text-sm font-mono max-h-48 overflow-y-auto">
                      {connectionLogs[selectedIntegration].map((log, index) => (
                        <div key={index} className="py-1">{log}</div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Real-time Sync Toggle */}
              {currentIntegration.status === 'connected' && (
                <div className="flex items-center justify-between p-4 bg-gray-900/50 rounded border border-gray-700">
                  <div>
                    <div className="text-white font-medium">Real-time Synchronization</div>
                    <div className="text-gray-400 text-sm">Enable continuous data sync with {currentIntegration.name}</div>
                  </div>
                  <Switch
                    checked={realTimeSync[selectedIntegration] || false}
                    onCheckedChange={(checked) => 
                      setRealTimeSync(prev => ({ ...prev, [selectedIntegration]: checked }))
                    }
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Key className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium">Select an Integration</h3>
                <p className="text-sm">Choose an integration from the list to configure its settings</p>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};
