
import React from 'react';
import { Card } from '@/components/ui/card';
import { Network, Timer } from 'lucide-react';
import { Integration } from '@/types/integrations';
import { getCategoryIcon } from '@/utils/integrationIcons';

interface IntegrationsMonitoringProps {
  integrations: { [key: string]: Integration };
}

export const IntegrationsMonitoring: React.FC<IntegrationsMonitoringProps> = ({ integrations }) => {
  return (
    <div className="grid lg:grid-cols-2 gap-6">
      {/* Connection Status */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Network className="w-5 h-5 mr-2" />
          Connection Health
        </h3>
        <div className="space-y-3">
          {Object.values(integrations)
            .filter(i => i.status === 'connected')
            .map((integration) => (
              <div key={integration.id} className="flex items-center justify-between p-3 bg-gray-900/30 rounded">
                <div className="flex items-center space-x-3">
                  {getCategoryIcon(integration.category)}
                  <div>
                    <div className="text-white font-medium">{integration.name}</div>
                    <div className="text-gray-400 text-sm">
                      Last sync: {integration.lastSync?.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span className="text-green-400 text-sm">Active</span>
                </div>
              </div>
            ))}
        </div>
      </Card>

      {/* Performance Metrics */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Timer className="w-5 h-5 mr-2" />
          Performance Metrics
        </h3>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-900/30 rounded">
              <div className="text-2xl font-bold text-green-400">99.9%</div>
              <div className="text-gray-400 text-sm">Uptime</div>
            </div>
            <div className="text-center p-3 bg-gray-900/30 rounded">
              <div className="text-2xl font-bold text-blue-400">1.2s</div>
              <div className="text-gray-400 text-sm">Avg Response</div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-900/30 rounded">
              <div className="text-2xl font-bold text-purple-400">2.4M</div>
              <div className="text-gray-400 text-sm">API Calls/Day</div>
            </div>
            <div className="text-center p-3 bg-gray-900/30 rounded">
              <div className="text-2xl font-bold text-yellow-400">0.02%</div>
              <div className="text-gray-400 text-sm">Error Rate</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
