
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Filter, Download } from 'lucide-react';
import { Integration } from '@/types/integrations';
import { categoryNames } from '@/types/integrations';
import { getCategoryIcon, getStatusIcon } from '@/utils/integrationIcons';

interface IntegrationsOverviewProps {
  groupedIntegrations: { [key: string]: Integration[] };
  realTimeSync: { [key: string]: boolean };
  setSelectedIntegration: (id: string) => void;
}

export const IntegrationsOverview: React.FC<IntegrationsOverviewProps> = ({
  groupedIntegrations,
  realTimeSync,
  setSelectedIntegration
}) => {
  return (
    <div className="space-y-6">
      {Object.entries(groupedIntegrations).map(([category, categoryIntegrations]) => (
        <Card key={category} className="p-6 bg-gray-800/50 border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="text-blue-400">
                {getCategoryIcon(category)}
              </div>
              <h3 className="text-lg font-semibold text-white">
                {categoryNames[category as keyof typeof categoryNames] || category}
              </h3>
              <Badge variant="secondary" className="bg-gray-700 text-gray-300 border-gray-600">
                {categoryIntegrations.filter(i => i.status === 'connected').length} / {categoryIntegrations.length}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-700/50 hover:bg-gray-600 hover:text-white">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-700/50 hover:bg-gray-600 hover:text-white">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categoryIntegrations.map((integration) => (
              <div 
                key={integration.id}
                className="p-4 bg-gray-900/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-white">{integration.name}</h4>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(integration.status)}
                    {realTimeSync[integration.id] && (
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" title="Real-time sync active" />
                    )}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Status:</span>
                    <Badge 
                      variant={integration.status === 'connected' ? 'default' : 'outline'}
                      className={`text-xs ${
                        integration.status === 'connected' 
                          ? 'bg-green-500/20 text-green-400 border-green-500' 
                          : integration.status === 'error'
                          ? 'bg-red-500/20 text-red-400 border-red-500'
                          : 'bg-gray-500/20 text-gray-400 border-gray-500'
                      }`}
                    >
                      {integration.status}
                    </Badge>
                  </div>
                  
                  {integration.lastSync && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Last Sync:</span>
                      <span className="text-white text-xs">
                        {integration.lastSync.toLocaleTimeString()}
                      </span>
                    </div>
                  )}

                  {integration.rateLimits && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Rate Limit:</span>
                      <span className="text-gray-300 text-xs">
                        {integration.rateLimits.requests}/{integration.rateLimits.period}
                      </span>
                    </div>
                  )}
                  
                  <Button
                    onClick={() => setSelectedIntegration(integration.id)}
                    variant="outline"
                    size="sm"
                    className="w-full text-white border-gray-600 bg-gray-700/50 hover:bg-gray-600 hover:text-white"
                  >
                    Configure
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
};
