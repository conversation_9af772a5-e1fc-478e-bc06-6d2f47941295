
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Setting<PERSON>, Brain } from 'lucide-react';

export type ResponseStyle = 'concise' | 'detailed' | 'empathetic';
export type Personality = 'professional' | 'friendly' | 'formal' | 'casual';
export type Dialect = 'us' | 'uk' | 'au' | 'de-de' | 'de-at' | 'de-ch' | 'tr-tr' | 'tr-cy';

interface AIBehaviorSettingsProps {
  responseStyle: ResponseStyle;
  personality: Personality;
  dialect: Dialect;
  onResponseStyleChange: (style: ResponseStyle) => void;
  onPersonalityChange: (personality: Personality) => void;
  onDialectChange: (dialect: Dialect) => void;
}

export const AIBehaviorSettings = ({
  responseStyle,
  personality,
  dialect,
  onResponseStyleChange,
  onPersonalityChange,
  onDialectChange
}: AIBehaviorSettingsProps) => {
  const responseStyles = [
    { value: 'concise' as ResponseStyle, label: 'Concise', description: 'Short, direct answers' },
    { value: 'detailed' as ResponseStyle, label: 'Detailed', description: 'Comprehensive explanations' },
    { value: 'empathetic' as ResponseStyle, label: 'Empathetic', description: 'Understanding and supportive' }
  ];

  const personalities = [
    { value: 'professional' as Personality, label: 'Professional', description: 'Business-focused approach' },
    { value: 'friendly' as Personality, label: 'Friendly', description: 'Warm and approachable' },
    { value: 'formal' as Personality, label: 'Formal', description: 'Traditional and respectful' },
    { value: 'casual' as Personality, label: 'Casual', description: 'Relaxed and informal' }
  ];

  const dialects = [
    { value: 'us' as Dialect, label: 'US English', flag: '🇺🇸' },
    { value: 'uk' as Dialect, label: 'UK English', flag: '🇬🇧' },
    { value: 'au' as Dialect, label: 'Australian English', flag: '🇦🇺' },
    { value: 'de-de' as Dialect, label: 'German (Germany)', flag: '🇩🇪' },
    { value: 'de-at' as Dialect, label: 'German (Austria)', flag: '🇦🇹' },
    { value: 'de-ch' as Dialect, label: 'German (Switzerland)', flag: '🇨🇭' },
    { value: 'tr-tr' as Dialect, label: 'Turkish (Turkey)', flag: '🇹🇷' },
    { value: 'tr-cy' as Dialect, label: 'Turkish (Cyprus)', flag: '🇨🇾' }
  ];

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Brain className="w-5 h-5 mr-2" />
        AI Behavior Settings
      </h3>

      <div className="space-y-6">
        {/* Response Style */}
        <div>
          <Label className="text-white text-sm font-medium mb-2 block">Response Style</Label>
          <div className="grid grid-cols-1 gap-2">
            {responseStyles.map((style) => (
              <Button
                key={style.value}
                variant={responseStyle === style.value ? 'default' : 'outline'}
                onClick={() => onResponseStyleChange(style.value)}
                className={`justify-start h-auto p-3 ${
                  responseStyle === style.value
                    ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                    : 'bg-white/20 border border-white/30 text-purple hover:bg-white/30 hover:text-white'
                }`}
              >
                <div className="text-left">
                  <div className="font-medium">{style.label}</div>
                  <div className="text-xs opacity-70">{style.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Personality */}
        <div>
          <Label className="text-white text-sm font-medium mb-2 block">AI Personality</Label>
          <div className="grid grid-cols-2 gap-2">
            {personalities.map((person) => (
              <Button
                key={person.value}
                variant={personality === person.value ? 'default' : 'outline'}
                onClick={() => onPersonalityChange(person.value)}
                className={`h-auto p-3 ${
                  personality === person.value
                    ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                    : 'bg-white/20 border border-white/30 text-purple hover:bg-white/30 hover:text-white'
                }`}
              >
                <div className="text-center">
                  <div className="font-medium text-sm">{person.label}</div>
                  <div className="text-xs opacity-70">{person.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Dialect Selection */}
        <div>
          <Label className="text-white text-sm font-medium mb-2 block">Language Dialect</Label>
          <Select value={dialect} onValueChange={onDialectChange}>
            <SelectTrigger className="bg-white/10 border-white/30 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {dialects.map((dialectOption) => (
                <SelectItem key={dialectOption.value} value={dialectOption.value}>
                  <span className="flex items-center">
                    <span className="mr-2">{dialectOption.flag}</span>
                    {dialectOption.label}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </Card>
  );
};
