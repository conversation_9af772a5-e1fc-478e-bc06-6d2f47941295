
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { VoiceStatus } from '@/pages/Index';
import { Mi<PERSON>, Brain, MessageSquare, Volume2 } from 'lucide-react';

interface RealTimeInteractionFeedbackProps {
  status: VoiceStatus;
}

export const RealTimeInteractionFeedback = ({ status }: RealTimeInteractionFeedbackProps) => {
  const [audioLevels, setAudioLevels] = useState<number[]>(new Array(20).fill(0));
  const [transcription, setTranscription] = useState('');
  const [statusMessage, setStatusMessage] = useState('');

  // Simulate real-time audio levels
  useEffect(() => {
    if (status === 'listening' || status === 'speaking') {
      const interval = setInterval(() => {
        const newLevels = Array.from({ length: 20 }, () => Math.random() * 100);
        setAudioLevels(newLevels);
      }, 100);
      return () => clearInterval(interval);
    } else {
      setAudioLevels(new Array(20).fill(0));
    }
  }, [status]);

  // Simulate real-time transcription
  useEffect(() => {
    if (status === 'listening') {
      const phrases = [
        'Hello, I need help with...',
        'Hello, I need help with my account...',
        'Hello, I need help with my account settings...'
      ];
      let index = 0;
      const interval = setInterval(() => {
        if (index < phrases.length) {
          setTranscription(phrases[index]);
          index++;
        }
      }, 1000);
      return () => clearInterval(interval);
    } else if (status === 'processing') {
      setTranscription('Processing your request...');
    } else if (status === 'speaking') {
      setTranscription('AI: I\'d be happy to help you with your account settings. Let me guide you through the process...');
    }
  }, [status]);

  // Status messages
  useEffect(() => {
    switch (status) {
      case 'listening':
        setStatusMessage('Listening to your voice...');
        break;
      case 'processing':
        setStatusMessage('AI is thinking and analyzing...');
        break;
      case 'speaking':
        setStatusMessage('AI is responding...');
        break;
      default:
        setStatusMessage('Ready to start conversation');
    }
  }, [status]);

  return (
    <Card className="p-6 bg-white/10 backdrop-blur-sm border-white/20">
      <div className="space-y-6">
        {/* Status Bar */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {status === 'listening' && <Mic className="w-5 h-5 text-green-400 animate-pulse" />}
            {status === 'processing' && <Brain className="w-5 h-5 text-yellow-400 animate-pulse" />}
            {status === 'speaking' && <Volume2 className="w-5 h-5 text-blue-400 animate-pulse" />}
            <span className="text-white font-medium">{statusMessage}</span>
          </div>
          <Badge 
            className={`${
              status === 'listening' ? 'bg-green-600' :
              status === 'processing' ? 'bg-yellow-600' :
              status === 'speaking' ? 'bg-blue-600' : 'bg-gray-600'
            } text-white`}
          >
            {status.toUpperCase()}
          </Badge>
        </div>

        {/* Voice Activity Chart */}
        <div className="space-y-2">
          <h4 className="text-white text-sm font-medium flex items-center">
            <Mic className="w-4 h-4 mr-2" />
            Audio Input Levels
          </h4>
          <div className="flex items-end space-x-1 h-16 bg-black/20 rounded-lg p-2">
            {audioLevels.map((level, index) => (
              <div
                key={index}
                className={`flex-1 rounded-t transition-all duration-100 ${
                  level > 70 ? 'bg-red-400' :
                  level > 40 ? 'bg-yellow-400' :
                  level > 10 ? 'bg-green-400' : 'bg-gray-600'
                }`}
                style={{ height: `${Math.max(2, (level / 100) * 100)}%` }}
              />
            ))}
          </div>
        </div>

        {/* Real-time Transcription */}
        <div className="space-y-2">
          <h4 className="text-white text-sm font-medium flex items-center">
            <MessageSquare className="w-4 h-4 mr-2" />
            Live Transcription
          </h4>
          <div className="bg-black/20 rounded-lg p-3 min-h-[60px] flex items-center">
            <p className="text-white/90 text-sm">
              {transcription}
              {status === 'listening' && <span className="animate-pulse">|</span>}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};
