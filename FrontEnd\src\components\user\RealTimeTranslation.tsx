
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Languages, ArrowRightLeft } from 'lucide-react';

interface RealTimeTranslationProps {
  translationEnabled: boolean;
  sourceLanguage: string;
  targetLanguage: string;
  onToggleTranslation: (enabled: boolean) => void;
  onSourceLanguageChange: (language: string) => void;
  onTargetLanguageChange: (language: string) => void;
}

export const RealTimeTranslation = ({
  translationEnabled,
  sourceLanguage,
  targetLanguage,
  onToggleTranslation,
  onSourceLanguageChange,
  onTargetLanguageChange
}: RealTimeTranslationProps) => {
  const [currentTranscript, setCurrentTranscript] = useState({
    original: "Hello, how can I help you today?",
    translated: "<PERSON><PERSON>, wie kann ich Ihnen heute helfen?"
  });

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'tr', name: 'Turkish', flag: '🇹🇷' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' }
  ];

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Languages className="w-5 h-5 mr-2" />
        Real-Time Translation
      </h3>

      <div className="space-y-4">
        {/* Translation Toggle */}
        <div className="flex items-center justify-between">
          <Label className="text-white text-sm">Enable Translation</Label>
          <Switch
            checked={translationEnabled}
            onCheckedChange={onToggleTranslation}
          />
        </div>

        {translationEnabled && (
          <>
            {/* Language Selection */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-white text-sm mb-2 block">From</Label>
                <Select value={sourceLanguage} onValueChange={onSourceLanguageChange}>
                  <SelectTrigger className="bg-white/10 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <span className="flex items-center">
                          <span className="mr-2">{lang.flag}</span>
                          {lang.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-white text-sm mb-2 block">To</Label>
                <Select value={targetLanguage} onValueChange={onTargetLanguageChange}>
                  <SelectTrigger className="bg-white/10 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <span className="flex items-center">
                          <span className="mr-2">{lang.flag}</span>
                          {lang.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Swap Languages Button */}
            <div className="flex justify-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  onSourceLanguageChange(targetLanguage);
                  onTargetLanguageChange(sourceLanguage);
                }}
                className="text-white hover:bg-white/20"
              >
                <ArrowRightLeft className="w-4 h-4" />
              </Button>
            </div>

            {/* Live Translation Display */}
            <div className="space-y-3">
              <div className="p-3 bg-white/10 rounded-lg">
                <Label className="text-white text-sm font-medium">Original</Label>
                <p className="text-white/90 text-sm mt-1">{currentTranscript.original}</p>
              </div>
              
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <Label className="text-white text-sm font-medium">Translation</Label>
                <p className="text-white/90 text-sm mt-1">{currentTranscript.translated}</p>
              </div>
            </div>
          </>
        )}
      </div>
    </Card>
  );
};
