
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Play, Star, MessageSquare, Clock, BarChart3, Lightbulb } from 'lucide-react';

export const UserDashboardWidgets = () => {
  const [aiLearningEnabled, setAiLearningEnabled] = useState(true);
  
  const recentInteractions = [
    {
      id: '1',
      summary: 'Account password reset assistance',
      timestamp: '2 minutes ago',
      duration: '3:45',
      satisfaction: 'positive',
      flagged: false
    },
    {
      id: '2',
      summary: 'Billing inquiry and plan upgrade',
      timestamp: '1 hour ago',
      duration: '5:23',
      satisfaction: 'positive',
      flagged: true
    },
    {
      id: '3',
      summary: 'Technical support for integration',
      timestamp: '2 hours ago',
      duration: '8:12',
      satisfaction: 'neutral',
      flagged: false
    }
  ];

  const suggestedQuestions = [
    'How do I update my billing information?',
    'What are the available integration options?',
    'How can I enhance account security?',
    'What features are included in the premium plan?'
  ];

  const toggleFavorite = (id: string) => {
    console.log(`Toggling favorite for interaction ${id}`);
  };

  const replayInteraction = (id: string) => {
    console.log(`Replaying interaction ${id}`);
  };

  return (
    <div className="space-y-6 mb-8">
      {/* Quick Stats */}
      <div className="grid md:grid-cols-4 gap-4">
        <Card className="p-4 bg-white/20 backdrop-blur-sm border-white/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Total Conversations</p>
              <p className="text-white text-2xl font-bold">24</p>
            </div>
            <MessageSquare className="w-8 h-8 text-blue-400" />
          </div>
        </Card>
        
        <Card className="p-4 bg-white/20 backdrop-blur-sm border-white/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Avg Duration</p>
              <p className="text-white text-2xl font-bold">4:32</p>
            </div>
            <Clock className="w-8 h-8 text-green-400" />
          </div>
        </Card>
        
        <Card className="p-4 bg-white/20 backdrop-blur-sm border-white/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Success Rate</p>
              <p className="text-white text-2xl font-bold">92%</p>
            </div>
            <BarChart3 className="w-8 h-8 text-yellow-400" />
          </div>
        </Card>
        
        <Card className="p-4 bg-white/20 backdrop-blur-sm border-white/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Satisfaction</p>
              <p className="text-white text-2xl font-bold">4.8/5</p>
            </div>
            <Star className="w-8 h-8 text-purple-400" />
          </div>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Recent Interactions */}
        <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
          <h3 className="text-white text-lg font-semibold mb-4">Recent Interactions</h3>
          <div className="space-y-3">
            {recentInteractions.map((interaction) => (
              <div
                key={interaction.id}
                className="p-3 bg-white/10 rounded-lg border border-white/20"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-white text-sm font-medium">{interaction.summary}</h4>
                    {interaction.flagged && (
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleFavorite(interaction.id)}
                      className="text-white hover:bg-white/20 p-1"
                    >
                      <Star className={`w-4 h-4 ${interaction.flagged ? 'fill-current text-yellow-400' : ''}`} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => replayInteraction(interaction.id)}
                      className="text-white hover:bg-white/20 p-1"
                    >
                      <Play className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs text-white/70">
                  <span>{interaction.timestamp}</span>
                  <div className="flex items-center space-x-2">
                    <span>{interaction.duration}</span>
                    <Badge 
                      className={`${
                        interaction.satisfaction === 'positive' ? 'bg-green-600' :
                        interaction.satisfaction === 'negative' ? 'bg-red-600' : 'bg-yellow-600'
                      } text-white text-xs`}
                    >
                      {interaction.satisfaction}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Suggested Questions & AI Learning */}
        <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
          <div className="space-y-6">
            {/* AI Learning Toggle */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white text-lg font-semibold">AI Personalization</h3>
                <Switch
                  checked={aiLearningEnabled}
                  onCheckedChange={setAiLearningEnabled}
                />
              </div>
              <p className="text-white/70 text-sm">
                {aiLearningEnabled 
                  ? 'AI is learning from your interactions to provide better responses'
                  : 'AI learning is disabled. Responses will be standard.'
                }
              </p>
            </div>

            {/* Suggested Questions */}
            <div>
              <h4 className="text-white font-medium mb-3 flex items-center">
                <Lightbulb className="w-4 h-4 mr-2" />
                Suggested Questions
              </h4>
              <div className="space-y-2">
                {suggestedQuestions.map((question, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full text-left justify-start border-white/30 text-white hover:bg-white/20 h-auto p-3"
                    onClick={() => console.log(`Selected question: ${question}`)}
                  >
                    <div className="text-sm">{question}</div>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
