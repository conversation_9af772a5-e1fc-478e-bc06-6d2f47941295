
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Moon, Sun, Bell, Keyboard } from 'lucide-react';

interface UserPreferencesProps {
  theme: 'light' | 'dark';
  notifications: boolean;
  keyboardShortcuts: boolean;
  onThemeChange: (theme: 'light' | 'dark') => void;
  onNotificationsChange: (enabled: boolean) => void;
  onKeyboardShortcutsChange: (enabled: boolean) => void;
}

export const UserPreferences = ({
  theme,
  notifications,
  keyboardShortcuts,
  onThemeChange,
  onNotificationsChange,
  onKeyboardShortcutsChange
}: UserPreferencesProps) => {
  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Settings className="w-5 h-5 mr-2" />
        User Preferences
      </h3>

      <div className="space-y-4">
        {/* Theme Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {theme === 'dark' ? (
              <Moon className="w-4 h-4 text-white" />
            ) : (
              <Sun className="w-4 h-4 text-white" />
            )}
            <Label className="text-white text-sm">Dark Mode</Label>
          </div>
          <Switch
            checked={theme === 'dark'}
            onCheckedChange={(checked) => onThemeChange(checked ? 'dark' : 'light')}
          />
        </div>

        {/* Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="w-4 h-4 text-white" />
            <Label className="text-white text-sm">Notifications</Label>
          </div>
          <Switch
            checked={notifications}
            onCheckedChange={onNotificationsChange}
          />
        </div>

        {/* Keyboard Shortcuts */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Keyboard className="w-4 h-4 text-white" />
            <Label className="text-white text-sm">Keyboard Shortcuts</Label>
          </div>
          <Switch
            checked={keyboardShortcuts}
            onCheckedChange={onKeyboardShortcutsChange}
          />
        </div>

        {keyboardShortcuts && (
          <div className="mt-4 p-3 bg-white/10 rounded-lg">
            <Label className="text-white text-sm font-medium mb-2 block">Shortcuts</Label>
            <div className="space-y-1 text-xs text-white/80">
              <div>Space: Start/Stop Voice</div>
              <div>Ctrl + M: Mute/Unmute</div>
              <div>Ctrl + T: Transfer to Human</div>
              <div>Ctrl + H: Toggle History</div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};
