
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';

interface VoiceActivityMonitorProps {
  isListening: boolean;
  noiseSuppressionEnabled: boolean;
  onToggleNoiseSuppression: (enabled: boolean) => void;
}

export const VoiceActivityMonitor = ({
  isListening,
  noiseSuppressionEnabled,
  onToggleNoiseSuppression
}: VoiceActivityMonitorProps) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const [backgroundNoise, setBackgroundNoise] = useState(0);
  const [waveformData, setWaveformData] = useState<number[]>(new Array(20).fill(0));

  // Simulate audio level detection and waveform
  useEffect(() => {
    if (isListening) {
      const interval = setInterval(() => {
        const newAudioLevel = Math.random() * 100;
        const newBackgroundNoise = Math.random() * 30;
        
        setAudioLevel(newAudioLevel);
        setBackgroundNoise(newBackgroundNoise);
        
        // Update waveform data
        setWaveformData(prev => {
          const newData = [...prev.slice(1), newAudioLevel];
          return newData;
        });
      }, 100);
      return () => clearInterval(interval);
    } else {
      setAudioLevel(0);
      setBackgroundNoise(0);
      setWaveformData(new Array(20).fill(0));
    }
  }, [isListening]);

  const getActivityColor = (level: number) => {
    if (level > 70) return 'bg-red-500';
    if (level > 40) return 'bg-yellow-500';
    if (level > 10) return 'bg-green-500';
    return 'bg-gray-500';
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Mic className="w-5 h-5 mr-2" />
        Voice Activity Monitor
      </h3>

      <div className="space-y-4">
        {/* Animated Voice Visualization */}
        <div className="flex justify-center mb-6">
          <div className="relative w-32 h-32 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            {/* Outer pulsing ring */}
            <div className={`absolute inset-0 rounded-full ${
              isListening ? 'animate-ping bg-blue-400/30' : ''
            }`} />
            
            {/* Waveform visualization */}
            <div className="absolute inset-4 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center overflow-hidden">
              <div className="flex items-center justify-center space-x-1 h-full">
                {waveformData.map((level, index) => (
                  <div
                    key={index}
                    className="bg-white/70 rounded-full transition-all duration-100"
                    style={{
                      width: '2px',
                      height: `${Math.max(4, (level / 100) * 40)}px`,
                      animation: isListening ? `pulse 0.${index + 5}s ease-in-out infinite alternate` : 'none'
                    }}
                  />
                ))}
              </div>
            </div>
            
            {/* Center icon */}
            <div className="relative z-10">
              {isListening ? (
                <Mic className="w-8 h-8 text-white" />
              ) : (
                <MicOff className="w-8 h-8 text-white/60" />
              )}
            </div>
          </div>
        </div>

        {/* Noise Suppression Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {noiseSuppressionEnabled ? (
              <VolumeX className="w-4 h-4 text-white" />
            ) : (
              <Volume2 className="w-4 h-4 text-white" />
            )}
            <Label className="text-white text-sm">Background Noise Filtering</Label>
          </div>
          <Switch
            checked={noiseSuppressionEnabled}
            onCheckedChange={onToggleNoiseSuppression}
          />
        </div>

        {/* Audio Level Meters */}
        <div className="space-y-3">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label className="text-white text-sm">Voice Level</Label>
              <span className="text-white text-xs">{Math.round(audioLevel)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-100 ${getActivityColor(audioLevel)}`}
                style={{ width: `${audioLevel}%` }}
              />
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-1">
              <Label className="text-white text-sm">Background Noise</Label>
              <span className="text-white text-xs">{Math.round(backgroundNoise)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-100 ${
                  noiseSuppressionEnabled ? 'bg-blue-500' : 'bg-orange-500'
                }`}
                style={{ width: `${backgroundNoise}%` }}
              />
            </div>
          </div>
        </div>

        {/* Full Duplex Communication Status */}
        <div className="text-center">
          <div className="text-sm text-white/80">
            Mode: {isListening ? 'Full Duplex Active' : 'Standby'}
          </div>
          <div className="text-xs text-white/60">
            {isListening ? 'Listening and ready to respond' : 'Click to start conversation'}
          </div>
          {noiseSuppressionEnabled && (
            <div className="text-xs text-blue-300">Noise suppression active</div>
          )}
        </div>
      </div>
    </Card>
  );
};
