
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';

interface VoiceActivityMonitorProps {
  isListening: boolean;
  noiseSuppressionEnabled: boolean;
  onToggleNoiseSuppression: (enabled: boolean) => void;
}

export const VoiceActivityMonitor = ({
  isListening,
  noiseSuppressionEnabled,
  onToggleNoiseSuppression
}: VoiceActivityMonitorProps) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const [backgroundNoise, setBackgroundNoise] = useState(0);

  // Simulate audio level detection
  useEffect(() => {
    if (isListening) {
      const interval = setInterval(() => {
        setAudioLevel(Math.random() * 100);
        setBackgroundNoise(Math.random() * 30);
      }, 100);
      return () => clearInterval(interval);
    } else {
      setAudioLevel(0);
      setBackgroundNoise(0);
    }
  }, [isListening]);

  const getActivityColor = (level: number) => {
    if (level > 70) return 'bg-red-500';
    if (level > 40) return 'bg-yellow-500';
    if (level > 10) return 'bg-green-500';
    return 'bg-gray-500';
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Mic className="w-5 h-5 mr-2" />
        Voice Activity Monitor
      </h3>

      <div className="space-y-4">
        {/* Noise Suppression Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {noiseSuppressionEnabled ? (
              <VolumeX className="w-4 h-4 text-white" />
            ) : (
              <Volume2 className="w-4 h-4 text-white" />
            )}
            <Label className="text-white text-sm">Background Noise Filtering</Label>
          </div>
          <Switch
            checked={noiseSuppressionEnabled}
            onCheckedChange={onToggleNoiseSuppression}
          />
        </div>

        {/* Audio Level Meters */}
        <div className="space-y-3">
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label className="text-white text-sm">Voice Level</Label>
              <span className="text-white text-xs">{Math.round(audioLevel)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-100 ${getActivityColor(audioLevel)}`}
                style={{ width: `${audioLevel}%` }}
              />
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-1">
              <Label className="text-white text-sm">Background Noise</Label>
              <span className="text-white text-xs">{Math.round(backgroundNoise)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-100 ${
                  noiseSuppressionEnabled ? 'bg-blue-500' : 'bg-orange-500'
                }`}
                style={{ width: `${backgroundNoise}%` }}
              />
            </div>
          </div>
        </div>

        {/* Visual Feedback */}
        <div className="flex justify-center">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 ${
            isListening ? 'bg-green-500 scale-110' : 'bg-gray-600'
          }`}>
            {isListening ? (
              <Mic className="w-8 h-8 text-white animate-pulse" />
            ) : (
              <MicOff className="w-8 h-8 text-white" />
            )}
          </div>
        </div>

        {/* Quality Indicator */}
        <div className="text-center">
          <div className="text-sm text-white/80">
            Quality: {audioLevel > 50 ? 'Excellent' : audioLevel > 20 ? 'Good' : 'Poor'}
          </div>
          {noiseSuppressionEnabled && (
            <div className="text-xs text-blue-300">Noise suppression active</div>
          )}
        </div>
      </div>
    </Card>
  );
};
