
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User, Bot } from 'lucide-react';
import { ConversationEntry } from '@/types/conversation';

interface ConversationHistoryDisplayProps {
  conversationHistory: ConversationEntry[];
  onClearHistory: () => void;
}

export const ConversationHistoryDisplay = ({ 
  conversationHistory, 
  onClearHistory 
}: ConversationHistoryDisplayProps) => {
  return (
    <Card className="p-6 bg-gray-900/50 border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Conversation History</h3>
        {conversationHistory.length > 0 && (
          <Button
            onClick={onClearHistory}
            variant="outline"
            size="sm"
            className="text-white border-gray-600"
          >
            Clear History
          </Button>
        )}
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {conversationHistory.length === 0 ? (
          <p className="text-gray-400 text-center py-8">No conversation yet. Start by speaking!</p>
        ) : (
          conversationHistory.map((entry) => (
            <div
              key={entry.id}
              className={`flex items-start space-x-3 p-3 rounded-lg ${
                entry.type === 'user' 
                  ? 'bg-blue-500/10 border border-blue-500/20' 
                  : 'bg-green-500/10 border border-green-500/20'
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                entry.type === 'user' ? 'bg-blue-600' : 'bg-green-600'
              }`}>
                {entry.type === 'user' ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium text-white">
                    {entry.type === 'user' ? 'You' : 'AI Assistant'}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {entry.language.toUpperCase()}
                  </Badge>
                  {entry.confidence && (
                    <Badge variant="outline" className="text-xs text-green-400 border-green-400">
                      {Math.round(entry.confidence)}% confidence
                    </Badge>
                  )}
                  <span className="text-xs text-gray-400">
                    {entry.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="text-gray-300">{entry.content}</p>
              </div>
            </div>
          ))
        )}
      </div>
    </Card>
  );
};
