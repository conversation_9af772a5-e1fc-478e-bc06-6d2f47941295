
import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Heart, Brain, TrendingUp, AlertTriangle } from 'lucide-react';

interface EmotionDetectorProps {
  transcript: string;
  emotion: string;
  onEmotionChange: (emotion: string) => void;
}

export const EmotionDetector = ({ 
  transcript, 
  emotion, 
  onEmotionChange 
}: EmotionDetectorProps) => {
  const [emotionScores, setEmotionScores] = useState({
    neutral: 60,
    happy: 20,
    frustrated: 10,
    sad: 5,
    urgent: 5
  });
  
  const [emotionHistory, setEmotionHistory] = useState<string[]>([]);
  const [riskLevel, setRiskLevel] = useState<'low' | 'medium' | 'high'>('low');

  useEffect(() => {
    if (transcript && transcript.length > 10) {
      analyzeEmotion(transcript);
    }
  }, [transcript]);

  const analyzeEmotion = async (text: string) => {
    // Enhanced emotion detection with sentiment analysis
    const emotionPatterns = {
      frustrated: {
        keywords: ['angry', 'frustrated', 'annoyed', 'upset', 'mad', 'terrible', 'awful', 'hate'],
        intensity: 2
      },
      happy: {
        keywords: ['happy', 'great', 'good', 'excellent', 'wonderful', 'amazing', 'perfect', 'love'],
        intensity: 1
      },
      sad: {
        keywords: ['sad', 'disappointed', 'unhappy', 'down', 'depressed', 'miserable'],
        intensity: 1.5
      },
      urgent: {
        keywords: ['urgent', 'emergency', 'quickly', 'asap', 'immediately', 'hurry', 'fast'],
        intensity: 2
      },
      confused: {
        keywords: ['confused', 'don\'t understand', 'unclear', 'what', 'how', 'help', 'lost'],
        intensity: 1
      }
    };

    const lowerText = text.toLowerCase();
    const newScores = { ...emotionScores };
    let detectedEmotion = 'neutral';
    let maxScore = 0;

    // Reset scores
    Object.keys(newScores).forEach(key => {
      newScores[key as keyof typeof newScores] = 10;
    });

    // Analyze text for emotion patterns
    for (const [emotionType, pattern] of Object.entries(emotionPatterns)) {
      let score = 0;
      pattern.keywords.forEach(keyword => {
        if (lowerText.includes(keyword)) {
          score += pattern.intensity * 20;
        }
      });
      
      if (score > 0) {
        newScores[emotionType as keyof typeof newScores] = Math.min(100, score);
        if (score > maxScore) {
          maxScore = score;
          detectedEmotion = emotionType;
        }
      }
    }

    // Calculate neutral score
    newScores.neutral = Math.max(0, 100 - Object.values(newScores).reduce((a, b) => a + b, 0) + newScores.neutral);

    setEmotionScores(newScores);
    
    // Update emotion history
    setEmotionHistory(prev => [...prev.slice(-4), detectedEmotion]);
    
    // Determine risk level
    const frustrationLevel = newScores.frustrated + newScores.sad;
    const urgencyLevel = newScores.urgent;
    
    if (frustrationLevel > 60 || urgencyLevel > 70) {
      setRiskLevel('high');
    } else if (frustrationLevel > 30 || urgencyLevel > 40) {
      setRiskLevel('medium');
    } else {
      setRiskLevel('low');
    }

    onEmotionChange(detectedEmotion);
  };

  const getEmotionColor = (emotionType: string) => {
    const colors = {
      neutral: 'text-gray-400',
      happy: 'text-green-400',
      frustrated: 'text-red-400',
      sad: 'text-blue-400',
      urgent: 'text-orange-400'
    };
    return colors[emotionType as keyof typeof colors] || 'text-gray-400';
  };

  const getEmotionIcon = (emotionType: string) => {
    switch (emotionType) {
      case 'happy':
        return '😊';
      case 'frustrated':
        return '😤';
      case 'sad':
        return '😢';
      case 'urgent':
        return '⚡';
      default:
        return '😐';
    }
  };

  const getRiskColor = () => {
    switch (riskLevel) {
      case 'high':
        return 'border-red-500 bg-red-500/10';
      case 'medium':
        return 'border-yellow-500 bg-yellow-500/10';
      default:
        return 'border-green-500 bg-green-500/10';
    }
  };

  return (
    <Card className={`p-4 ${getRiskColor()}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Heart className="w-5 h-5 text-pink-400" />
          <h3 className="text-white font-semibold">Emotion Analysis</h3>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className={`${getEmotionColor(emotion)} border-current`}>
            {getEmotionIcon(emotion)} {emotion.toUpperCase()}
          </Badge>
          
          {riskLevel === 'high' && (
            <Badge variant="destructive">
              <AlertTriangle className="w-3 h-3 mr-1" />
              HIGH RISK
            </Badge>
          )}
        </div>
      </div>

      {/* Emotion Scores */}
      <div className="space-y-2 mb-4">
        {Object.entries(emotionScores).map(([emotionType, score]) => (
          <div key={emotionType}>
            <div className="flex justify-between text-sm mb-1">
              <span className={`capitalize ${getEmotionColor(emotionType)}`}>
                {getEmotionIcon(emotionType)} {emotionType}
              </span>
              <span className="text-white">{Math.round(score)}%</span>
            </div>
            <Progress value={score} className="h-1" />
          </div>
        ))}
      </div>

      {/* Emotion History */}
      <div className="flex items-center space-x-2">
        <Brain className="w-4 h-4 text-purple-400" />
        <span className="text-purple-200 text-sm">Recent emotions:</span>
        <div className="flex space-x-1">
          {emotionHistory.slice(-5).map((emo, index) => (
            <span key={index} className="text-sm">
              {getEmotionIcon(emo)}
            </span>
          ))}
        </div>
      </div>

      {/* AI Recommendations */}
      {riskLevel === 'high' && (
        <div className="mt-3 p-2 bg-red-500/20 rounded border border-red-500/30">
          <p className="text-red-300 text-sm">
            🚨 Customer shows high frustration. Consider:
            • Offering immediate escalation
            • Providing compensation options
            • Using empathetic language
          </p>
        </div>
      )}
      
      {emotion === 'urgent' && (
        <div className="mt-3 p-2 bg-orange-500/20 rounded border border-orange-500/30">
          <p className="text-orange-300 text-sm">
            ⚡ Urgent request detected. Prioritize quick resolution and clear timelines.
          </p>
        </div>
      )}
    </Card>
  );
};
