import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Mic, 
  Brain, 
  MessageSquare,
  Zap,
  Globe,
  AlertCircle,
  CheckCircle,
  Loader2,
  Play,
  Volume2,
  Pause,
  Square
} from 'lucide-react';
import { useHuggingFaceVoiceEngine } from '@/hooks/useHuggingFaceVoiceEngine';

interface EnhancedVoiceEngineControllerProps {
  language: 'en' | 'de' | 'tr';
  onConversationUpdate: (conversation: any) => void;
}

export const EnhancedVoiceEngineController = ({ 
  language, 
  onConversationUpdate 
}: EnhancedVoiceEngineControllerProps) => {
  const { toast } = useToast();
  const [industry, setIndustry] = useState<'healthcare' | 'finance' | 'ecommerce' | 'insurance' | 'retail' | 'telecommunications' | 'general'>('general');
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);
  const [customerSatisfaction, setCustomerSatisfaction] = useState(85);
  const [userId] = useState(() => `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [huggingFaceConfig, setHuggingFaceConfig] = useState<any>(null);
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  
  // Prevent duplicate conversation updates
  const lastConversationRef = useRef<string>('');
  const conversationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load API configurations on component mount
  useEffect(() => {
    const loadAPIConfigurations = () => {
      try {
        const savedConfigs = localStorage.getItem('apiConfigurations');
        if (savedConfigs) {
          const parsedConfigs = JSON.parse(savedConfigs);
          
          const config = {
            apiKey: parsedConfigs.stt?.apiKey || '',
            endpoints: {
              speechToText: parsedConfigs.stt?.endpoint || '',
              textGeneration: parsedConfigs.llm?.endpoint || '',
              textToSpeech: parsedConfigs.tts?.endpoint || '',
              emotionDetection: parsedConfigs.emotion?.endpoint || '',
              intentClassification: parsedConfigs.orchestration?.endpoint || ''
            }
          };
          
          setHuggingFaceConfig(config);
          console.log('Loaded API configuration:', config);
        } else {
          toast({
            title: "No API Configuration Found",
            description: "Please configure your API endpoints in the Admin Dashboard first.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error loading API configurations:', error);
        toast({
          title: "Configuration Error",
          description: "Failed to load API configurations.",
          variant: "destructive"
        });
      }
    };

    loadAPIConfigurations();
  }, [toast]);

  // Check microphone permissions
  useEffect(() => {
    const checkMicrophonePermission = async () => {
      try {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        setMicrophonePermission(permission.state as any);
        
        permission.addEventListener('change', () => {
          setMicrophonePermission(permission.state as any);
        });
      } catch (error) {
        console.log('Could not check microphone permission:', error);
      }
    };

    checkMicrophonePermission();
  }, []);

  const {
    isListening,
    isProcessing,
    isSpeaking,
    audioLevel,
    confidence,
    currentTranscript,
    aiResponse,
    emotion,
    intent,
    conversationState,
    contextMemory,
    sessionActive,
    fullDuplexMode,
    startFullDuplexMode,
    stopFullDuplexMode,
    synthesizeSpeech
  } = useHuggingFaceVoiceEngine({ 
    language, 
    industry, 
    config: huggingFaceConfig || { apiKey: '', endpoints: {} }
  });

  const handleStartFullDuplexSession = async () => {
    if (!huggingFaceConfig || !huggingFaceConfig.apiKey) {
      toast({
        title: "API Configuration Required",
        description: "Please configure your Hugging Face API key in the Admin Dashboard first.",
        variant: "destructive"
      });
      return;
    }

    // Check microphone permission
    if (microphonePermission === 'denied') {
      toast({
        title: "Microphone Access Denied",
        description: "Please enable microphone access in your browser settings to use voice features.",
        variant: "destructive"
      });
      return;
    }

    try {
      await startFullDuplexMode();
      
      toast({
        title: "Full Duplex Mode Started",
        description: "AI agent is now in continuous conversation mode. Just speak naturally!",
      });
    } catch (error) {
      console.error('Failed to start full duplex session:', error);
      toast({
        title: "Failed to Start Session",
        description: "Could not access microphone. Please check your browser permissions.",
        variant: "destructive"
      });
    }
  };

  const handleStopSession = () => {
    stopFullDuplexMode();
    
    toast({
      title: "Conversation Ended",
      description: "AI agent has stopped the continuous conversation.",
    });
  };

  // Fixed conversation update handling to prevent duplicates
  useEffect(() => {
    if (currentTranscript && aiResponse) {
      const conversationKey = `${currentTranscript}-${aiResponse}`;
      
      // Check if this is a duplicate
      if (lastConversationRef.current === conversationKey) {
        console.log('Duplicate conversation detected, skipping...');
        return;
      }
      
      // Clear any existing timeout
      if (conversationTimeoutRef.current) {
        clearTimeout(conversationTimeoutRef.current);
      }
      
      // Set a timeout to process the conversation update
      conversationTimeoutRef.current = setTimeout(() => {
        const newEntry = {
          id: Date.now(),
          timestamp: new Date(),
          userInput: currentTranscript,
          aiResponse: aiResponse,
          emotion: emotion,
          intent: intent,
          confidence: confidence,
          language: language,
          industry: industry
        };
        
        console.log('Processing new conversation entry:', newEntry);
        setConversationHistory(prev => [...prev, newEntry]);
        onConversationUpdate(newEntry);
        
        // Store the key to prevent duplicates
        lastConversationRef.current = conversationKey;
        
        // Clear the key after a delay
        setTimeout(() => {
          lastConversationRef.current = '';
        }, 2000);
      }, 300); // Small delay to batch updates
    }
  }, [currentTranscript, aiResponse, emotion, intent, confidence, language, industry, onConversationUpdate]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (conversationTimeoutRef.current) {
        clearTimeout(conversationTimeoutRef.current);
      }
    };
  }, []);

  const getStatusColor = () => {
    if (isListening) return 'bg-green-500';
    if (isProcessing) return 'bg-yellow-500';
    if (isSpeaking) return 'bg-blue-500';
    if (fullDuplexMode) return 'bg-purple-500';
    return 'bg-gray-500';
  };

  const getStatusText = () => {
    if (isListening) return 'Listening for your voice...';
    if (isProcessing) return 'AI is thinking...';
    if (isSpeaking) return 'AI is speaking...';
    if (fullDuplexMode && !isListening && !isProcessing && !isSpeaking) return 'Ready - speak anytime!';
    return 'Click to start continuous conversation';
  };

  const getStatusIcon = () => {
    if (isListening) return <Mic className="w-8 h-8" />;
    if (isProcessing) return <Loader2 className="w-8 h-8 animate-spin" />;
    if (isSpeaking) return <Volume2 className="w-8 h-8" />;
    if (fullDuplexMode) return <Square className="w-6 h-6" />;
    return <Play className="w-8 h-8" />;
  };

  // Show configuration warning if not properly set up
  if (!huggingFaceConfig || !huggingFaceConfig.apiKey) {
    return (
      <Card className="p-6 bg-gradient-to-br from-red-900 to-orange-900 border-red-500/30">
        <div className="flex items-center space-x-3 mb-4">
          <AlertCircle className="w-6 h-6 text-red-400" />
          <h3 className="text-xl font-bold text-white">API Configuration Required</h3>
        </div>
        <p className="text-red-200 mb-4">
          Please configure your API endpoints and keys in the Admin Dashboard before using the voice engine.
        </p>
        <div className="text-sm text-red-100">
          <p>Required configurations:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Hugging Face API Key</li>
            <li>Speech-to-Text Endpoint</li>
            <li>Language Model Endpoint</li>
            <li>Text-to-Speech Endpoint</li>
          </ul>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Voice Control Interface */}
      <Card className="p-6 bg-gradient-to-br from-purple-900 to-blue-900 border-purple-500/30">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-4 h-4 rounded-full animate-pulse ${getStatusColor()}`} />
            <h3 className="text-xl font-bold text-white">Full Duplex AI Voice Agent</h3>
            <Badge variant="outline" className="text-purple-300 border-purple-300">
              {language.toUpperCase()}
            </Badge>
            <Badge variant="outline" className="text-blue-300 border-blue-300">
              <Zap className="w-3 h-3 mr-1" />
              Continuous
            </Badge>
            {microphonePermission === 'granted' && (
              <Badge variant="outline" className="text-green-300 border-green-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Mic Ready
              </Badge>
            )}
            {fullDuplexMode && (
              <Badge className="bg-green-600 text-white animate-pulse">
                LIVE CONVERSATION
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Globe className="w-4 h-4 text-green-400" />
              <Select value={industry} onValueChange={(value) => setIndustry(value as typeof industry)}>
                <SelectTrigger className="w-40 bg-gray-800 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="ecommerce">E-commerce</SelectItem>
                  <SelectItem value="insurance">Insurance</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="telecommunications">Telecom</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Enhanced Voice Visualization */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            {fullDuplexMode && (
              <>
                <div className="absolute inset-0 w-32 h-32 rounded-full bg-purple-500/20 animate-ping" />
                <div className="absolute inset-2 w-28 h-28 rounded-full bg-purple-500/30 animate-ping animation-delay-75" />
                <div className="absolute inset-4 w-24 h-24 rounded-full bg-purple-500/40 animate-ping animation-delay-150" />
              </>
            )}
            
            <Button
              onClick={fullDuplexMode ? handleStopSession : handleStartFullDuplexSession}
              size="lg"
              disabled={isProcessing && !fullDuplexMode}
              className={`w-20 h-20 rounded-full transition-all duration-300 ${
                fullDuplexMode 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {getStatusIcon()}
            </Button>
          </div>
        </div>

        {/* Status and Metrics */}
        <div className="space-y-4">
          <div className="text-center">
            <p className="text-white font-medium">{getStatusText()}</p>
            <p className="text-purple-200 text-sm">
              {fullDuplexMode ? 'Continuous conversation active - no button pressing needed!' : 'Click to start automatic conversation flow'}
            </p>
            {microphonePermission === 'denied' && (
              <p className="text-red-400 text-sm mt-2">
                Microphone access is required. Please enable it in your browser settings.
              </p>
            )}
          </div>

          {fullDuplexMode && (
            <>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-purple-200">Voice Activity Level</span>
                  <span className="text-white">{Math.round(audioLevel)}%</span>
                </div>
                <Progress value={audioLevel} className="h-2" />
              </div>

              {confidence > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-purple-200">AI Understanding</span>
                    <span className="text-white">{Math.round(confidence)}%</span>
                  </div>
                  <Progress value={confidence} className="h-2" />
                </div>
              )}

              {/* Conversation Stats */}
              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{contextMemory.length}</div>
                  <div className="text-xs text-gray-400">Exchanges</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {fullDuplexMode ? 'LIVE' : 'IDLE'}
                  </div>
                  <div className="text-xs text-gray-400">Status</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{industry}</div>
                  <div className="text-xs text-gray-400">Context</div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Control Instructions */}
        {fullDuplexMode && (
          <div className="mt-6 p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <Mic className="w-4 h-4 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Continuous Mode Active</span>
            </div>
            <p className="text-white text-sm">
              Just speak naturally! The AI will automatically detect when you're speaking and respond. 
              No need to press any buttons. The conversation will continue until you stop it.
            </p>
          </div>
        )}
      </Card>

      {/* Real-time Transcript Display */}
      {fullDuplexMode && (currentTranscript || aiResponse) && (
        <Card className="p-4 bg-gray-900/50 border-gray-700">
          <div className="space-y-3">
            {currentTranscript && (
              <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <MessageSquare className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 text-sm font-medium">You:</span>
                  {intent && (
                    <Badge variant="outline" className="text-xs text-blue-300 border-blue-300">
                      {intent.replace('_', ' ')}
                    </Badge>
                  )}
                </div>
                <p className="text-white">{currentTranscript}</p>
              </div>
            )}
            
            {aiResponse && (
              <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <Brain className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm font-medium">AI Agent:</span>
                  <Badge variant="outline" className="text-xs text-green-300 border-green-300">
                    {industry}
                  </Badge>
                </div>
                <p className="text-white">{aiResponse}</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Context Memory Display */}
      {contextMemory.length > 0 && (
        <Card className="p-4 bg-gray-900/50 border-gray-700">
          <div className="flex items-center space-x-2 mb-3">
            <Brain className="w-5 h-5 text-orange-400" />
            <h4 className="text-white font-medium">Conversation Memory</h4>
            <Badge variant="outline" className="text-orange-300 border-orange-300">
              {contextMemory.length} exchanges
            </Badge>
          </div>
          <div className="space-y-2">
            {contextMemory.slice(-3).map((ctx, idx) => (
              <div key={idx} className="text-sm bg-gray-800/50 p-2 rounded">
                <div className="text-blue-300">You: {ctx.user}</div>
                <div className="text-green-300">AI: {ctx.ai}</div>
                <div className="text-xs text-gray-400 mt-1">
                  Intent: {ctx.intent}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};
