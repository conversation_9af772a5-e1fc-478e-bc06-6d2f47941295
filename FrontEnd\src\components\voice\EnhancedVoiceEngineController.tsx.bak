
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Mic, 
  MicOff, 
  Brain, 
  Heart,
  Phone,
  MessageSquare,
  Settings,
  Zap,
  Globe,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useHuggingFaceVoiceEngine } from '@/hooks/useHuggingFaceVoiceEngine';
import { IntentRecognition } from './IntentRecognition';
import { ConversationContext } from './ConversationContext';
import { EmotionDetector } from './EmotionDetector';

interface EnhancedVoiceEngineControllerProps {
  language: 'en' | 'de' | 'tr';
  onConversationUpdate: (conversation: any) => void;
}

export const EnhancedVoiceEngineController = ({ 
  language, 
  onConversationUpdate 
}: EnhancedVoiceEngineControllerProps) => {
  const { toast } = useToast();
  const [sessionActive, setSessionActive] = useState(false);
  const [industry, setIndustry] = useState<'healthcare' | 'finance' | 'ecommerce' | 'insurance' | 'retail' | 'telecommunications' | 'general'>('general');
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);
  const [customerSatisfaction, setCustomerSatisfaction] = useState(85);
  const [userId] = useState(() => `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [huggingFaceConfig, setHuggingFaceConfig] = useState<any>(null);
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');

  // Load API configurations on component mount
  useEffect(() => {
    const loadAPIConfigurations = () => {
      try {
        const savedConfigs = localStorage.getItem('apiConfigurations');
        if (savedConfigs) {
          const parsedConfigs = JSON.parse(savedConfigs);
          
          // Extract Hugging Face configuration
          const config = {
            apiKey: parsedConfigs.stt?.apiKey || '',
            endpoints: {
              speechToText: parsedConfigs.stt?.endpoint || '',
              textGeneration: parsedConfigs.llm?.endpoint || '',
              textToSpeech: parsedConfigs.tts?.endpoint || '',
              emotionDetection: parsedConfigs.emotion?.endpoint || '',
              intentClassification: parsedConfigs.orchestration?.endpoint || ''
            }
          };
          
          setHuggingFaceConfig(config);
          console.log('Loaded API configuration:', config);
        } else {
          toast({
            title: "No API Configuration Found",
            description: "Please configure your API endpoints in the Admin Dashboard first.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error loading API configurations:', error);
        toast({
          title: "Configuration Error",
          description: "Failed to load API configurations.",
          variant: "destructive"
        });
      }
    };

    loadAPIConfigurations();
  }, [toast]);

  // Check microphone permissions
  useEffect(() => {
    const checkMicrophonePermission = async () => {
      try {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        setMicrophonePermission(permission.state as any);
        
        permission.addEventListener('change', () => {
          setMicrophonePermission(permission.state as any);
        });
      } catch (error) {
        console.log('Could not check microphone permission:', error);
      }
    };

    checkMicrophonePermission();
  }, []);

  const {
    isListening,
    isProcessing,
    isSpeaking,
    audioLevel,
    confidence,
    currentTranscript,
    aiResponse,
    emotion,
    intent,
    conversationState,
    contextMemory,
    startListening,
    stopListening,
    synthesizeSpeech
  } = useHuggingFaceVoiceEngine({ 
    language, 
    industry, 
    config: huggingFaceConfig || { apiKey: '', endpoints: {} }
  });

  const handleStartSession = async () => {
    if (!huggingFaceConfig || !huggingFaceConfig.apiKey) {
      toast({
        title: "API Configuration Required",
        description: "Please configure your Hugging Face API key in the Admin Dashboard first.",
        variant: "destructive"
      });
      return;
    }

    if (!huggingFaceConfig.endpoints.speechToText) {
      toast({
        title: "Speech-to-Text Endpoint Required",
        description: "Please configure the Speech-to-Text endpoint in the Admin Dashboard.",
        variant: "destructive"
      });
      return;
    }

    // Check microphone permission
    if (microphonePermission === 'denied') {
      toast({
        title: "Microphone Access Denied",
        description: "Please enable microphone access in your browser settings to use voice features.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Request microphone access if needed
      if (microphonePermission === 'prompt') {
        await navigator.mediaDevices.getUserMedia({ audio: true });
      }

      setSessionActive(true);
      await startListening();
      
      toast({
        title: "Voice Session Started",
        description: "AI agent is now listening. Speak clearly into your microphone.",
      });
    } catch (error) {
      console.error('Failed to start session:', error);
      toast({
        title: "Failed to Start Session",
        description: "Could not access microphone. Please check your browser permissions.",
        variant: "destructive"
      });
      setSessionActive(false);
    }
  };

  const handleEndSession = () => {
    setSessionActive(false);
    stopListening();
    
    toast({
      title: "Voice Session Ended",
      description: "AI agent has stopped listening.",
    });
  };

  const handleTransferToHuman = () => {
    console.log('Transferring to human agent...');
    // Implement human transfer logic
  };

  const handleIndustryChange = (newIndustry: string) => {
    setIndustry(newIndustry as typeof industry);
  };

  useEffect(() => {
    if (currentTranscript && aiResponse) {
      const newEntry = {
        id: Date.now(),
        timestamp: new Date(),
        userInput: currentTranscript,
        aiResponse: aiResponse,
        emotion: emotion,
        intent: intent,
        confidence: confidence,
        language: language,
        industry: industry
      };
      
      setConversationHistory(prev => [...prev, newEntry]);
      onConversationUpdate(newEntry);
    }
  }, [currentTranscript, aiResponse, emotion, intent, confidence, language, industry, onConversationUpdate]);

  const getStatusColor = () => {
    if (isListening) return 'bg-green-500';
    if (isProcessing) return 'bg-yellow-500';
    if (isSpeaking) return 'bg-blue-500';
    return 'bg-gray-500';
  };

  const getStatusText = () => {
    if (isListening) return 'Listening to customer...';
    if (isProcessing) return 'AI is analyzing...';
    if (isSpeaking) return 'AI is responding...';
    return 'Ready for enterprise-grade conversation';
  };

  const getPerformanceMetrics = () => {
    const avgConfidence = contextMemory.length > 0 
      ? contextMemory.reduce((sum, ctx) => sum + confidence, 0) / contextMemory.length 
      : 0;
    
    const responseTime = isProcessing ? '2.1s' : '1.8s';
    const resolutionRate = Math.round(customerSatisfaction * 0.95);
    
    return { avgConfidence, responseTime, resolutionRate };
  };

  const metrics = getPerformanceMetrics();

  // Show configuration warning if not properly set up
  if (!huggingFaceConfig || !huggingFaceConfig.apiKey) {
    return (
      <Card className="p-6 bg-gradient-to-br from-red-900 to-orange-900 border-red-500/30">
        <div className="flex items-center space-x-3 mb-4">
          <AlertCircle className="w-6 h-6 text-red-400" />
          <h3 className="text-xl font-bold text-white">API Configuration Required</h3>
        </div>
        <p className="text-red-200 mb-4">
          Please configure your API endpoints and keys in the Admin Dashboard before using the voice engine.
        </p>
        <div className="text-sm text-red-100">
          <p>Required configurations:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Hugging Face API Key</li>
            <li>Speech-to-Text Endpoint</li>
            <li>Language Model Endpoint</li>
            <li>Text-to-Speech Endpoint</li>
          </ul>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Voice Control Interface */}
      <Card className="p-6 bg-gradient-to-br from-purple-900 to-blue-900 border-purple-500/30">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-4 h-4 rounded-full animate-pulse ${getStatusColor()}`} />
            <h3 className="text-xl font-bold text-white">Enterprise AI Voice Agent</h3>
            <Badge variant="outline" className="text-purple-300 border-purple-300">
              {language.toUpperCase()}
            </Badge>
            <Badge variant="outline" className="text-blue-300 border-blue-300">
              <Zap className="w-3 h-3 mr-1" />
              Hugging Face
            </Badge>
            {microphonePermission === 'granted' && (
              <Badge variant="outline" className="text-green-300 border-green-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Mic Ready
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Globe className="w-4 h-4 text-green-400" />
              <Select value={industry} onValueChange={(value) => setIndustry(value as typeof industry)}>
                <SelectTrigger className="w-40 bg-gray-800 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="ecommerce">E-commerce</SelectItem>
                  <SelectItem value="insurance">Insurance</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="telecommunications">Telecom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Heart className="w-5 h-5 text-red-400" />
              <span className="text-white text-sm">Satisfaction: {customerSatisfaction}%</span>
            </div>
          </div>
        </div>

        {/* Voice Visualization */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            {sessionActive && (
              <>
                <div className="absolute inset-0 w-32 h-32 rounded-full bg-purple-500/20 animate-ping" />
                <div className="absolute inset-2 w-28 h-28 rounded-full bg-purple-500/30 animate-ping animation-delay-75" />
                <div className="absolute inset-4 w-24 h-24 rounded-full bg-purple-500/40 animate-ping animation-delay-150" />
              </>
            )}
            
            <Button
              onClick={sessionActive ? handleEndSession : handleStartSession}
              size="lg"
              disabled={isProcessing}
              className={`w-20 h-20 rounded-full transition-all duration-300 ${
                sessionActive 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {isProcessing ? (
                <Loader2 className="w-8 h-8 animate-spin" />
              ) : sessionActive ? (
                <Phone className="w-8 h-8" />
              ) : (
                <Mic className="w-8 h-8" />
              )}
            </Button>
          </div>
        </div>

        {/* Status and Metrics */}
        <div className="space-y-4">
          <div className="text-center">
            <p className="text-white font-medium">{getStatusText()}</p>
            <p className="text-purple-200 text-sm">
              {sessionActive ? 'Enterprise-grade full duplex conversation' : 'Click to start AI agent'}
            </p>
            {microphonePermission === 'denied' && (
              <p className="text-red-400 text-sm mt-2">
                Microphone access is required. Please enable it in your browser settings.
              </p>
            )}
          </div>

          {sessionActive && (
            <>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-purple-200">Voice Input Level</span>
                  <span className="text-white">{Math.round(audioLevel)}%</span>
                </div>
                <Progress value={audioLevel} className="h-2" />
              </div>

              {confidence > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-purple-200">AI Confidence</span>
                    <span className="text-white">{Math.round(confidence)}%</span>
                  </div>
                  <Progress value={confidence} className="h-2" />
                </div>
              )}

              {/* Real-time Performance Metrics */}
              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{metrics.responseTime}</div>
                  <div className="text-xs text-gray-400">Response Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{Math.round(metrics.avgConfidence)}%</div>
                  <div className="text-xs text-gray-400">Avg Confidence</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{metrics.resolutionRate}%</div>
                  <div className="text-xs text-gray-400">Resolution Rate</div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Control Buttons */}
        {sessionActive && (
          <div className="flex justify-center space-x-4 mt-6">
            <Button
              onClick={() => console.log('Transferring to human agent...')}
              variant="outline"
              className="text-white border-purple-300 hover:bg-purple-700"
            >
              <Users className="w-4 h-4 mr-2" />
              Transfer to Human
            </Button>
          </div>
        )}
      </Card>

      {/* Enhanced Intent Recognition */}
      <IntentRecognition
        transcript={currentTranscript}
        industry={industry}
        onIntentDetected={(detectedIntent) => {
          console.log('Intent detected:', detectedIntent);
        }}
      />

      {/* Conversation Context Management */}
      <ConversationContext
        userId={userId}
        onContextUpdate={(context) => {
          console.log('Context updated:', context);
        }}
      />

      {/* Enhanced Emotion Detection */}
      <EmotionDetector 
        transcript={currentTranscript}
        emotion={emotion}
        onEmotionChange={(newEmotion) => {
          console.log('Emotion changed:', newEmotion);
        }}
      />

      {/* Real-time Transcript Display */}
      {sessionActive && (currentTranscript || aiResponse) && (
        <Card className="p-4 bg-gray-900/50 border-gray-700">
          <div className="space-y-3">
            {currentTranscript && (
              <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <MessageSquare className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 text-sm font-medium">Customer:</span>
                  {intent && (
                    <Badge variant="outline" className="text-xs text-blue-300 border-blue-300">
                      {intent.replace('_', ' ')}
                    </Badge>
                  )}
                </div>
                <p className="text-white">{currentTranscript}</p>
              </div>
            )}
            
            {aiResponse && (
              <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <Brain className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm font-medium">AI Agent:</span>
                  <Badge variant="outline" className="text-xs text-green-300 border-green-300">
                    {industry}
                  </Badge>
                </div>
                <p className="text-white">{aiResponse}</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Context Memory Display */}
      {contextMemory.length > 0 && (
        <Card className="p-4 bg-gray-900/50 border-gray-700">
          <div className="flex items-center space-x-2 mb-3">
            <Brain className="w-5 h-5 text-orange-400" />
            <h4 className="text-white font-medium">Conversation Memory</h4>
            <Badge variant="outline" className="text-orange-300 border-orange-300">
              {contextMemory.length} exchanges
            </Badge>
          </div>
          <div className="space-y-2">
            {contextMemory.slice(-3).map((ctx, idx) => (
              <div key={idx} className="text-sm bg-gray-800/50 p-2 rounded">
                <div className="text-blue-300">User: {ctx.user}</div>
                <div className="text-green-300">AI: {ctx.ai}</div>
                <div className="text-xs text-gray-400 mt-1">
                  Emotion: {ctx.emotion} | Intent: {ctx.intent}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};
