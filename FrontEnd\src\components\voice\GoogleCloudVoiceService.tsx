
import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Brain, 
  Globe,
  Zap,
  Activity
} from 'lucide-react';

interface VoiceServiceProps {
  language: 'en' | 'de' | 'tr';
  onTranscriptUpdate: (transcript: string) => void;
  onResponseUpdate: (response: string) => void;
  onStatusUpdate: (status: string) => void;
}

export const GoogleCloudVoiceService = ({
  language,
  onTranscriptUpdate,
  onResponseUpdate,
  onStatusUpdate
}: VoiceServiceProps) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [confidence, setConfidence] = useState(0);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const languageConfig = {
    en: { 
      code: 'en-US', 
      name: 'English',
      voice: 'en-US-Studio-O',
      greeting: 'Hello! How can I assist you today?'
    },
    de: { 
      code: 'de-DE', 
      name: 'Deutsch',
      voice: 'de-DE-Studio-B',
      greeting: 'Hallo! Wie kann ich Ihnen heute helfen?'
    },
    tr: { 
      code: 'tr-TR', 
      name: 'Türkçe',
      voice: 'tr-TR-Standard-A',
      greeting: 'Merhaba! Size bugün nasıl yardımcı olabilirim?'
    }
  };

  // Initialize audio context and setup voice recognition
  const initializeAudioContext = async () => {
    try {
      setConnectionStatus('connecting');
      onStatusUpdate('Initializing voice system...');

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        }
      });

      streamRef.current = stream;
      audioContextRef.current = new AudioContext({ sampleRate: 16000 });
      analyserRef.current = audioContextRef.current.createAnalyser();
      
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      analyserRef.current.fftSize = 256;
      
      setConnectionStatus('connected');
      onStatusUpdate('Voice system ready');
      
      // Start audio level monitoring
      monitorAudioLevel();
      
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      setConnectionStatus('disconnected');
      onStatusUpdate('Failed to access microphone');
    }
  };

  // Monitor audio input levels
  const monitorAudioLevel = () => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    
    const updateLevel = () => {
      if (analyserRef.current && isListening) {
        analyserRef.current.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        setAudioLevel(Math.min(100, (average / 255) * 100));
        requestAnimationFrame(updateLevel);
      }
    };
    
    requestAnimationFrame(updateLevel);
  };

  // Simulate speech recognition (replace with actual Google Cloud Speech API)
  const startSpeechRecognition = async () => {
    if (!streamRef.current) return;

    setIsListening(true);
    onStatusUpdate('Listening...');
    
    // Simulate recognition process
    setTimeout(() => {
      const mockTranscripts = {
        en: ["Hello, can you help me?", "What's the weather like?", "I need assistance with my account"],
        de: ["Hallo, können Sie mir helfen?", "Wie ist das Wetter?", "Ich brauche Hilfe mit meinem Konto"],
        tr: ["Merhaba, bana yardım edebilir misiniz?", "Hava nasıl?", "Hesabımla ilgili yardıma ihtiyacım var"]
      };
      
      const randomTranscript = mockTranscripts[language][Math.floor(Math.random() * mockTranscripts[language].length)];
      setCurrentTranscript(randomTranscript);
      onTranscriptUpdate(randomTranscript);
      setConfidence(Math.random() * 30 + 70); // 70-100% confidence
      
      processAIResponse(randomTranscript);
    }, 2000 + Math.random() * 3000);
  };

  // Process AI response (simulate intelligent responses)
  const processAIResponse = async (transcript: string) => {
    setIsProcessing(true);
    onStatusUpdate('AI is thinking...');

    // Simulate AI processing time
    setTimeout(() => {
      const responses = {
        en: {
          greeting: "Hello! I'm your AI assistant. How can I help you today?",
          weather: "I'd be happy to help with weather information. Could you specify your location?",
          account: "I can assist you with your account. What specific issue are you experiencing?",
          default: "I understand. Let me help you with that. Could you provide more details?"
        },
        de: {
          greeting: "Hallo! Ich bin Ihr KI-Assistent. Wie kann ich Ihnen heute helfen?",
          weather: "Gerne helfe ich Ihnen mit Wetterinformationen. Könnten Sie Ihren Standort angeben?",
          account: "Ich kann Ihnen bei Ihrem Konto helfen. Welches spezifische Problem haben Sie?",
          default: "Ich verstehe. Lassen Sie mich Ihnen dabei helfen. Könnten Sie weitere Details angeben?"
        },
        tr: {
          greeting: "Merhaba! Ben sizin AI asistanınızım. Size bugün nasıl yardımcı olabilirim?",
          weather: "Hava durumu bilgileri konusunda size yardımcı olabilirim. Konumunuzu belirtebilir misiniz?",
          account: "Hesabınızla ilgili size yardımcı olabilirim. Hangi özel sorunu yaşıyorsunuz?",
          default: "Anlıyorum. Bu konuda size yardımcı olayım. Daha fazla detay verebilir misiniz?"
        }
      };

      let response = responses[language].default;
      
      if (transcript.toLowerCase().includes('hello') || transcript.toLowerCase().includes('hallo') || transcript.toLowerCase().includes('merhaba')) {
        response = responses[language].greeting;
      } else if (transcript.toLowerCase().includes('weather') || transcript.toLowerCase().includes('wetter') || transcript.toLowerCase().includes('hava')) {
        response = responses[language].weather;
      } else if (transcript.toLowerCase().includes('account') || transcript.toLowerCase().includes('konto') || transcript.toLowerCase().includes('hesap')) {
        response = responses[language].account;
      }

      setAiResponse(response);
      onResponseUpdate(response);
      setIsProcessing(false);
      setIsListening(false);
      onStatusUpdate('Response ready');
      
      // Simulate text-to-speech
      speakResponse(response);
    }, 1500 + Math.random() * 2000);
  };

  // Text-to-speech functionality
  const speakResponse = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = languageConfig[language].code;
      utterance.rate = 0.9;
      utterance.pitch = 1;
      
      utterance.onstart = () => onStatusUpdate('AI is speaking...');
      utterance.onend = () => onStatusUpdate('Ready for next interaction');
      
      speechSynthesis.speak(utterance);
    }
  };

  const stopListening = () => {
    setIsListening(false);
    setIsProcessing(false);
    onStatusUpdate('Stopped listening');
  };

  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  return (
    <Card className="p-6 bg-gray-900/50 border-gray-700">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Brain className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-semibold text-white">AI Voice Agent</h3>
            <Badge variant="outline" className="text-blue-400 border-blue-400">
              {languageConfig[language].name}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-400' : 
              connectionStatus === 'connecting' ? 'bg-yellow-400' : 'bg-red-400'
            }`} />
            <span className="text-sm text-gray-300 capitalize">{connectionStatus}</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center space-x-4">
          {connectionStatus === 'disconnected' && (
            <Button onClick={initializeAudioContext} className="bg-blue-600 hover:bg-blue-700">
              <Globe className="w-4 h-4 mr-2" />
              Initialize Voice System
            </Button>
          )}
          
          {connectionStatus === 'connected' && (
            <>
              <Button
                onClick={isListening ? stopListening : startSpeechRecognition}
                disabled={isProcessing}
                className={`${
                  isListening 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {isListening ? <MicOff className="w-4 h-4 mr-2" /> : <Mic className="w-4 h-4 mr-2" />}
                {isListening ? 'Stop Listening' : 'Start Voice Chat'}
              </Button>
              
              {aiResponse && (
                <Button
                  onClick={() => speakResponse(aiResponse)}
                  variant="outline"
                  className="text-white border-gray-600"
                >
                  <Volume2 className="w-4 h-4 mr-2" />
                  Replay Response
                </Button>
              )}
            </>
          )}
        </div>

        {/* Audio Level Indicator */}
        {isListening && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">Audio Input Level</span>
              <span className="text-white">{Math.round(audioLevel)}%</span>
            </div>
            <Progress value={audioLevel} className="h-2" />
          </div>
        )}

        {/* Confidence Score */}
        {confidence > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">AI Confidence</span>
              <span className="text-white">{Math.round(confidence)}%</span>
            </div>
            <Progress value={confidence} className="h-2" />
          </div>
        )}

        {/* Transcript Display */}
        {currentTranscript && (
          <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-600">
            <div className="flex items-center space-x-2 mb-2">
              <Activity className="w-4 h-4 text-green-400" />
              <span className="text-sm text-gray-400">You said:</span>
            </div>
            <p className="text-white">{currentTranscript}</p>
          </div>
        )}

        {/* AI Response Display */}
        {aiResponse && (
          <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <Brain className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-blue-400">AI Response:</span>
            </div>
            <p className="text-white">{aiResponse}</p>
          </div>
        )}

        {/* Processing Indicator */}
        {isProcessing && (
          <div className="flex items-center justify-center space-x-2 p-4">
            <Zap className="w-5 h-5 text-yellow-400 animate-pulse" />
            <span className="text-yellow-400">AI is processing your request...</span>
          </div>
        )}
      </div>
    </Card>
  );
};
