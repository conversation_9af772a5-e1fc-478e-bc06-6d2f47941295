
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Brain, Target, TrendingUp, AlertCircle } from 'lucide-react';

interface Intent {
  name: string;
  confidence: number;
  category: string;
  entities: { [key: string]: string };
  nextActions: string[];
}

interface IntentRecognitionProps {
  transcript: string;
  industry: 'healthcare' | 'finance' | 'ecommerce' | 'insurance' | 'retail' | 'telecommunications' | 'general';
  onIntentDetected: (intent: Intent) => void;
}

export const IntentRecognition = ({ 
  transcript, 
  industry, 
  onIntentDetected 
}: IntentRecognitionProps) => {
  const [currentIntent, setCurrentIntent] = useState<Intent | null>(null);
  const [intentHistory, setIntentHistory] = useState<Intent[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // Industry-specific intent patterns
  const industryIntents = {
    healthcare: {
      'appointment_booking': {
        keywords: ['appointment', 'schedule', 'book', 'visit', 'doctor', 'consultation'],
        category: 'scheduling',
        confidence: 0.9
      },
      'symptom_inquiry': {
        keywords: ['pain', 'symptoms', 'feel', 'hurt', 'sick', 'dizzy', 'fever'],
        category: 'medical',
        confidence: 0.95
      },
      'prescription_refill': {
        keywords: ['prescription', 'refill', 'medication', 'pills', 'medicine'],
        category: 'medication',
        confidence: 0.92
      },
      'insurance_verification': {
        keywords: ['insurance', 'coverage', 'copay', 'deductible', 'benefits'],
        category: 'billing',
        confidence: 0.88
      }
    },
    finance: {
      'account_balance': {
        keywords: ['balance', 'account', 'money', 'funds', 'available'],
        category: 'account_info',
        confidence: 0.93
      },
      'transaction_dispute': {
        keywords: ['dispute', 'fraud', 'unauthorized', 'charge', 'transaction'],
        category: 'dispute',
        confidence: 0.96
      },
      'loan_application': {
        keywords: ['loan', 'credit', 'mortgage', 'financing', 'borrow'],
        category: 'lending',
        confidence: 0.91
      },
      'investment_inquiry': {
        keywords: ['invest', 'portfolio', 'stocks', 'bonds', 'retirement'],
        category: 'investment',
        confidence: 0.89
      }
    },
    ecommerce: {
      'order_status': {
        keywords: ['order', 'shipment', 'delivery', 'tracking', 'status'],
        category: 'fulfillment',
        confidence: 0.94
      },
      'return_request': {
        keywords: ['return', 'refund', 'exchange', 'defective', 'wrong'],
        category: 'returns',
        confidence: 0.92
      },
      'product_inquiry': {
        keywords: ['product', 'item', 'specifications', 'features', 'details'],
        category: 'product_info',
        confidence: 0.87
      },
      'payment_issue': {
        keywords: ['payment', 'card', 'billing', 'charge', 'declined'],
        category: 'payment',
        confidence: 0.95
      }
    },
    insurance: {
      'claim_filing': {
        keywords: ['claim', 'accident', 'damage', 'incident', 'file'],
        category: 'claims',
        confidence: 0.96
      },
      'policy_inquiry': {
        keywords: ['policy', 'coverage', 'premium', 'deductible', 'benefits'],
        category: 'policy',
        confidence: 0.91
      },
      'quote_request': {
        keywords: ['quote', 'rate', 'price', 'cost', 'estimate'],
        category: 'sales',
        confidence: 0.89
      }
    },
    retail: {
      'store_hours': {
        keywords: ['hours', 'open', 'closed', 'time', 'when'],
        category: 'information',
        confidence: 0.85
      },
      'product_availability': {
        keywords: ['available', 'stock', 'inventory', 'in store', 'have'],
        category: 'inventory',
        confidence: 0.88
      }
    },
    telecommunications: {
      'service_outage': {
        keywords: ['outage', 'down', 'not working', 'connection', 'service'],
        category: 'technical',
        confidence: 0.94
      },
      'bill_inquiry': {
        keywords: ['bill', 'charge', 'usage', 'overage', 'fee'],
        category: 'billing',
        confidence: 0.92
      }
    },
    general: {
      'general_inquiry': {
        keywords: ['help', 'information', 'question', 'support', 'assist'],
        category: 'support',
        confidence: 0.75
      }
    }
  };

  const extractEntities = (text: string, intent: string) => {
    const entities: { [key: string]: string } = {};
    const lowerText = text.toLowerCase();

    // Extract common entities based on patterns
    const patterns = {
      date: /(\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b)|(\btomorrow\b)|(\btoday\b)|(\bnext week\b)/gi,
      time: /(\b\d{1,2}:\d{2}\s?(am|pm)?\b)|(\bmidnight\b)|(\bnoon\b)/gi,
      phone: /(\b\d{3}[-.]?\d{3}[-.]?\d{4}\b)/gi,
      email: /(\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b)/gi,
      amount: /(\$\d+(?:\.\d{2})?)|(\b\d+\s?(?:dollars?|euros?|pounds?)\b)/gi,
      account: /(\baccount\s?(?:number\s?)?\d+\b)|(\b\d{4,}\b)/gi
    };

    Object.entries(patterns).forEach(([key, pattern]) => {
      const matches = text.match(pattern);
      if (matches && matches.length > 0) {
        entities[key] = matches[0];
      }
    });

    return entities;
  };

  const getNextActions = (intent: string, category: string, industry: string) => {
    const actionMap: { [key: string]: string[] } = {
      'healthcare_scheduling': [
        'Check available appointment slots',
        'Verify insurance coverage',
        'Send appointment confirmation',
        'Add to calendar'
      ],
      'healthcare_medical': [
        'Schedule urgent consultation',
        'Provide medical disclaimer',
        'Transfer to nurse',
        'Document symptoms'
      ],
      'finance_account_info': [
        'Verify identity',
        'Provide account summary',
        'Offer additional services',
        'Send statement via email'
      ],
      'finance_dispute': [
        'Initiate dispute process',
        'Gather transaction details',
        'File fraud report',
        'Issue temporary credit'
      ],
      'ecommerce_fulfillment': [
        'Provide tracking information',
        'Check delivery status',
        'Offer delivery updates',
        'Handle delivery issues'
      ],
      'ecommerce_returns': [
        'Generate return label',
        'Process refund',
        'Schedule pickup',
        'Offer exchange options'
      ]
    };

    const key = `${industry}_${category}`;
    return actionMap[key] || [
      'Gather more information',
      'Escalate to specialist',
      'Provide general assistance',
      'Schedule follow-up'
    ];
  };

  const analyzeIntent = async (text: string) => {
    if (!text || text.length < 5) return;

    setIsProcessing(true);

    try {
      const intents = industryIntents[industry] || industryIntents.general;
      const lowerText = text.toLowerCase();
      let bestMatch: Intent | null = null;
      let highestScore = 0;

      // Analyze each intent pattern
      Object.entries(intents).forEach(([intentName, config]) => {
        let score = 0;
        let matchedKeywords = 0;

        config.keywords.forEach(keyword => {
          if (lowerText.includes(keyword.toLowerCase())) {
            score += config.confidence;
            matchedKeywords++;
          }
        });

        // Calculate final confidence based on keyword matches
        const finalConfidence = (score / config.keywords.length) * (matchedKeywords / config.keywords.length) * 100;

        if (finalConfidence > highestScore && finalConfidence > 30) {
          highestScore = finalConfidence;
          bestMatch = {
            name: intentName,
            confidence: finalConfidence,
            category: config.category,
            entities: extractEntities(text, intentName),
            nextActions: getNextActions(intentName, config.category, industry)
          };
        }
      });

      if (bestMatch) {
        setCurrentIntent(bestMatch);
        setIntentHistory(prev => [...prev.slice(-4), bestMatch!]);
        onIntentDetected(bestMatch);
      }

    } catch (error) {
      console.error('Intent analysis error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (transcript && transcript.length > 10) {
      analyzeIntent(transcript);
    }
  }, [transcript]);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-400 border-green-400';
    if (confidence >= 60) return 'text-yellow-400 border-yellow-400';
    return 'text-red-400 border-red-400';
  };

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: JSX.Element } = {
      scheduling: <Target className="w-4 h-4" />,
      medical: <AlertCircle className="w-4 h-4" />,
      account_info: <Brain className="w-4 h-4" />,
      dispute: <AlertCircle className="w-4 h-4" />,
      fulfillment: <TrendingUp className="w-4 h-4" />,
      support: <Brain className="w-4 h-4" />
    };
    return icons[category] || <Brain className="w-4 h-4" />;
  };

  return (
    <Card className="p-4 bg-gray-900/50 border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Brain className="w-5 h-5 text-purple-400" />
          <h3 className="text-white font-semibold">Intent Recognition</h3>
          <Badge variant="outline" className="text-purple-300 border-purple-300 capitalize">
            {industry}
          </Badge>
        </div>
        
        {isProcessing && (
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
            <span className="text-purple-300 text-sm">Analyzing...</span>
          </div>
        )}
      </div>

      {currentIntent && (
        <div className="space-y-4">
          {/* Current Intent */}
          <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/30">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                {getCategoryIcon(currentIntent.category)}
                <span className="text-white font-medium capitalize">
                  {currentIntent.name.replace(/_/g, ' ')}
                </span>
              </div>
              <Badge variant="outline" className={getConfidenceColor(currentIntent.confidence)}>
                {Math.round(currentIntent.confidence)}%
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-purple-200">Confidence</span>
                <span className="text-white">{Math.round(currentIntent.confidence)}%</span>
              </div>
              <Progress value={currentIntent.confidence} className="h-1" />
            </div>

            {/* Extracted Entities */}
            {Object.keys(currentIntent.entities).length > 0 && (
              <div className="mt-3">
                <span className="text-purple-200 text-sm">Extracted Information:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {Object.entries(currentIntent.entities).map(([key, value], idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {key}: {value}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Next Actions */}
            <div className="mt-3">
              <span className="text-purple-200 text-sm">Recommended Actions:</span>
              <ul className="text-xs text-gray-300 mt-1 space-y-1">
                {currentIntent.nextActions.slice(0, 3).map((action, idx) => (
                  <li key={idx} className="flex items-center space-x-2">
                    <span className="text-purple-400">→</span>
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Intent History */}
          {intentHistory.length > 0 && (
            <div>
              <span className="text-gray-400 text-sm">Recent Intents:</span>
              <div className="flex space-x-2 mt-1">
                {intentHistory.slice(-4).map((intent, idx) => (
                  <div key={idx} className="text-xs text-center">
                    <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center mb-1">
                      {getCategoryIcon(intent.category)}
                    </div>
                    <span className="text-gray-400 text-xs">
                      {Math.round(intent.confidence)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};
