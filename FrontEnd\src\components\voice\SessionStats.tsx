
import React from 'react';
import { Card } from '@/components/ui/card';
import { Clock, MessageSquare, TrendingUp, Zap } from 'lucide-react';
import { ConversationEntry } from '@/types/conversation';

interface SessionStatsProps {
  sessionDuration: number;
  conversationHistory: ConversationEntry[];
  currentStatus: string;
}

export const SessionStats = ({ 
  sessionDuration, 
  conversationHistory, 
  currentStatus 
}: SessionStatsProps) => {
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const avgConfidence = conversationHistory.filter(h => h.confidence).length > 0
    ? Math.round(conversationHistory.filter(h => h.confidence).reduce((acc, h) => acc + (h.confidence || 0), 0) / conversationHistory.filter(h => h.confidence).length)
    : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <div className="flex items-center space-x-3">
          <Clock className="w-5 h-5 text-blue-400" />
          <div>
            <p className="text-sm text-gray-400">Session Duration</p>
            <p className="text-lg font-semibold text-white">{formatDuration(sessionDuration)}</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <div className="flex items-center space-x-3">
          <MessageSquare className="w-5 h-5 text-green-400" />
          <div>
            <p className="text-sm text-gray-400">Exchanges</p>
            <p className="text-lg font-semibold text-white">{Math.floor(conversationHistory.length / 2)}</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <div className="flex items-center space-x-3">
          <TrendingUp className="w-5 h-5 text-purple-400" />
          <div>
            <p className="text-sm text-gray-400">Avg. Confidence</p>
            <p className="text-lg font-semibold text-white">{avgConfidence}%</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <div className="flex items-center space-x-3">
          <Zap className="w-5 h-5 text-yellow-400" />
          <div>
            <p className="text-sm text-gray-400">Status</p>
            <p className="text-lg font-semibold text-white">{currentStatus}</p>
          </div>
        </div>
      </Card>
    </div>
  );
};
