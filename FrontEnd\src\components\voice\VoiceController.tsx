
import React from 'react';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Phone, PhoneOff, Volume2 } from 'lucide-react';
import { VoiceStatus } from '@/pages/Index';

interface VoiceControllerProps {
  status: VoiceStatus;
  onStatusChange: (status: VoiceStatus) => void;
  onCallTransfer: () => void;
}

export const VoiceController = ({ status, onStatusChange, onCallTransfer }: VoiceControllerProps) => {
  const [isActive, setIsActive] = React.useState(false);
  const [isFullDuplex, setIsFullDuplex] = React.useState(false);
  const [waveformData, setWaveformData] = React.useState<number[]>(new Array(12).fill(0));

  // Simulate real-time waveform data
  React.useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        const newWaveform = Array.from({ length: 12 }, () => Math.random() * 100);
        setWaveformData(newWaveform);
      }, 150);
      return () => clearInterval(interval);
    } else {
      setWaveformData(new Array(12).fill(0));
    }
  }, [isActive]);

  const handleToggleVoice = () => {
    if (!isActive) {
      setIsActive(true);
      setIsFullDuplex(true);
      onStatusChange('listening');
      
      // Simulate full duplex - continuous listening and response capability
      const fullDuplexSimulation = () => {
        // Randomly switch between listening and speaking to simulate real conversation
        const states: VoiceStatus[] = ['listening', 'processing', 'speaking'];
        let currentStateIndex = 0;
        
        const interval = setInterval(() => {
          if (isActive) {
            currentStateIndex = (currentStateIndex + 1) % states.length;
            onStatusChange(states[currentStateIndex]);
          } else {
            clearInterval(interval);
          }
        }, Math.random() * 3000 + 1000); // Random intervals between 1-4 seconds
      };
      
      fullDuplexSimulation();
    } else {
      setIsActive(false);
      setIsFullDuplex(false);
      onStatusChange('idle');
    }
  };

  const handleTransferCall = () => {
    onCallTransfer();
    setIsActive(false);
    setIsFullDuplex(false);
    onStatusChange('idle');
  };

  return (
    <div className="flex flex-col items-center space-y-6">
      {/* Voice Visualization with Mic Icon */}
      <div className="relative flex items-center justify-center">
        {/* Outer pulsing rings */}
        {isActive && (
          <>
            <div className="absolute inset-0 w-32 h-32 rounded-full bg-gradient-to-r from-pink-500/30 to-purple-600/30 animate-ping" />
            <div className="absolute inset-2 w-28 h-28 rounded-full bg-gradient-to-r from-pink-500/20 to-purple-600/20 animate-ping animation-delay-75" />
            <div className="absolute inset-4 w-24 h-24 rounded-full bg-gradient-to-r from-pink-500/10 to-purple-600/10 animate-ping animation-delay-150" />
          </>
        )}
        
        {/* Main voice control button with waveform */}
        <div className="relative w-24 h-24">
          <Button
            onClick={handleToggleVoice}
            size="lg"
            className={`w-full h-full rounded-full transition-all duration-300 relative overflow-hidden ${
              isActive 
                ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-lg shadow-red-500/50' 
                : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg shadow-green-500/50'
            }`}
          >
            {/* Waveform visualization behind the icon */}
            {isActive && (
              <div className="absolute inset-0 flex items-center justify-center space-x-0.5 opacity-30">
                {waveformData.map((level, index) => (
                  <div
                    key={index}
                    className="bg-white/70 rounded-full transition-all duration-150"
                    style={{
                      width: '2px',
                      height: `${Math.max(4, (level / 100) * 60)}px`,
                      animation: `pulse ${0.8 + index * 0.1}s ease-in-out infinite alternate`
                    }}
                  />
                ))}
              </div>
            )}
            
            {/* Mic icon */}
            <div className="relative z-10">
              {isActive ? (
                <MicOff className="w-8 h-8" />
              ) : (
                <Mic className="w-8 h-8" />
              )}
            </div>
          </Button>
        </div>
      </div>

      <div className="text-center">
        <p className="text-white text-lg font-medium mb-2">
          {isActive ? 'Full Duplex Voice Agent' : 'Start Voice Interaction'}
        </p>
        <p className="text-white/70 text-sm">
          {isActive ? 'Listening continuously - speak anytime' : 'Click to begin AI conversation'}
        </p>
        {isFullDuplex && (
          <div className="flex items-center justify-center mt-2 space-x-2">
            <Volume2 className="w-4 h-4 text-green-300" />
            <span className="text-green-300 text-xs">Full Duplex Active</span>
          </div>
        )}
      </div>

      {/* Transfer to Human Button */}
      {isActive && (
        <div className="flex space-x-4">
          <Button
            onClick={handleTransferCall}
            variant="outline"
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            <Phone className="w-4 h-4 mr-2" />
            Transfer to Human
          </Button>
        </div>
      )}
    </div>
  );
};
