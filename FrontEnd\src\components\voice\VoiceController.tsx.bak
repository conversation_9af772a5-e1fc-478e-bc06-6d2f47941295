import React from 'react';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Phone, PhoneOff, Volume2 } from 'lucide-react';
import { VoiceStatus } from '@/pages/Index';

interface VoiceControllerProps {
  status: VoiceStatus;
  onStatusChange: (status: VoiceStatus) => void;
  onCallTransfer: () => void;
}

export const VoiceController = ({ status, onStatusChange, onCallTransfer }: VoiceControllerProps) => {
  const [isActive, setIsActive] = React.useState(false);
  const [isFullDuplex, setIsFullDuplex] = React.useState(false);
  const [waveformData, setWaveformData] = React.useState<number[]>(new Array(12).fill(0));

  // Simulate real-time waveform data
  React.useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        const newWaveform = Array.from({ length: 12 }, () => Math.random() * 100);
        setWaveformData(newWaveform);
      }, 150);
      return () => clearInterval(interval);
    } else {
      setWaveformData(new Array(12).fill(0));
    }
  }, [isActive]);

  const handleToggleVoice = () => {
    if (!isActive) {
      setIsActive(true);
      setIsFullDuplex(true);
      onStatusChange('listening');
      
      // Simulate full duplex - continuous listening and response capability
      const fullDuplexSimulation = () => {
        // Randomly switch between listening and speaking to simulate real conversation
        const states: VoiceStatus[] = ['listening', 'processing', 'speaking'];
        let currentStateIndex = 0;
        
        const interval = setInterval(() => {
          if (isActive) {
            currentStateIndex = (currentStateIndex + 1) % states.length;
            onStatusChange(states[currentStateIndex]);
          } else {
            clearInterval(interval);
          }
        }, Math.random() * 3000 + 1000); // Random intervals between 1-4 seconds
      };
      
      fullDuplexSimulation();
    } else {
      setIsActive(false);
      setIsFullDuplex(false);
      onStatusChange('idle');
    }
  };

  const handleTransferCall = () => {
    onCallTransfer();
    setIsActive(false);
    setIsFullDuplex(false);
    onStatusChange('idle');
  };

  return (
    <div className="flex flex-col items-center space-y-6">
      {/* Voice Visualization with Mic Icon */}
      <div className="relative flex items-center justify-center">
        {/* Outer pulsing rings */}
        {isActive && (
          <>
            <div className="absolute inset-0 w-32 h-32 rounded-full bg-gradient-to-r from-pink-500/30 to-purple-600/30 animate-ping" />
            <div className="absolute inset-2 w-28 h-28 rounded-full bg-gradient-to-r from-pink-500/20 to-purple-600/20 animate-ping animation-delay-75" />
            <div className="absolute inset-4 w-24 h-24 rounded-full bg-gradient-to-r from-pink-500/10 to-purple-600/10 animate-ping animation-delay-150" />
          </>
        )}
        
        {/* Main voice control button with animated waveform */}
        <div className="relative w-24 h-24">
          <Button
            onClick={handleToggleVoice}
            size="lg"
            className="w-full h-full rounded-full transition-all duration-300 relative overflow-hidden bg-transparent shadow-lg"
            style={{ background: "none" }}
          >
            {/* Animated SVG waveform */}
            <svg
              className="absolute inset-0"
              width="96"
              height="96"
              viewBox="0 0 96 96"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <defs>
                <radialGradient id="circleGradient" cx="50%" cy="50%" r="50%">
                  <stop offset="0%" stopColor="#ff00cc" />
                  <stop offset="100%" stopColor="#3333ff" />
                </radialGradient>
                <linearGradient id="waveGradient" x1="0" y1="48" x2="96" y2="48" gradientUnits="userSpaceOnUse">
                  <stop stopColor="#ff00cc" />
                  <stop offset="1" stopColor="#00ffe7" />
                </linearGradient>
              </defs>
              {/* Outer colorful ring */}
              <circle
                cx="48"
                cy="48"
                r="44"
                stroke="url(#circleGradient)"
                strokeWidth="4"
                fill="none"
              />
              {/* Animated waveform path */}
              <path>
                <animate
                  attributeName="d"
                  dur="2s"
                  repeatCount="indefinite"
                  values="
                    M16,48 Q32,24 48,48 Q64,72 80,48;
                    M16,48 Q32,72 48,48 Q64,24 80,48;
                    M16,48 Q32,24 48,48 Q64,72 80,48
                  "
                />
                <animate
                  attributeName="stroke"
                  values="#ff00cc;#00ffe7;#ff00cc"
                  dur="2s"
                  repeatCount="indefinite"
                />
              </path>
              <path
                d="M16,48 Q32,24 48,48 Q64,72 80,48"
                stroke="url(#waveGradient)"
                strokeWidth="4"
                fill="none"
                opacity="0.7"
              />
            </svg>
            {/* Mic icon centered */}
            <div className="relative z-10 flex items-center justify-center w-full h-full">
              {isActive ? (
                <MicOff className="w-8 h-8 text-white drop-shadow" />
              ) : (
                <Mic className="w-8 h-8 text-white drop-shadow" />
              )}
            </div>
          </Button>
        </div>
      </div>

      <div className="text-center">
        <p className="text-white text-lg font-medium mb-2">
          {isActive ? 'Full Duplex Voice Agent' : 'Start Voice Interaction'}
        </p>
        <p className="text-white/70 text-sm">
          {isActive ? 'Listening continuously - speak anytime' : 'Click to begin AI conversation'}
        </p>
        {isFullDuplex && (
          <div className="flex items-center justify-center mt-2 space-x-2">
            <Volume2 className="w-4 h-4 text-green-300" />
            <span className="text-green-300 text-xs">Full Duplex Active</span>
          </div>
        )}
      </div>

      {/* Transfer to Human Button */}
      {isActive && (
        <div className="flex space-x-4">
          <Button
            onClick={handleTransferCall}
            variant="outline"
            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
          >
            <Phone className="w-4 h-4 mr-2" />
            Transfer to Human
          </Button>
        </div>
      )}
    </div>
  );
};
