import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Brain, 
  Heart,
  Volume2,
  Settings,
  Phone,
  MessageSquare
} from 'lucide-react';
import { useVoiceEngine } from '@/hooks/useVoiceEngine';
import { EmotionDetector } from './EmotionDetector';
import { ConversationFlow } from './ConversationFlow';

interface VoiceEngineControllerProps {
  language: 'en' | 'de' | 'tr';
  onConversationUpdate: (conversation: any) => void;
}

export const VoiceEngineController = ({ 
  language, 
  onConversationUpdate 
}: VoiceEngineControllerProps) => {
  const {
    isListening,
    isProcessing,
    isSpeaking,
    audioLevel,
    confidence,
    currentTranscript,
    aiResponse,
    emotion,
    conversationState,
    startListening,
    stopListening,
    processWithAI,
    synthesizeSpeech
  } = useVoiceEngine({ language });

  const [sessionActive, setSessionActive] = useState(false);
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);
  const [customerSatisfaction, setCustomerSatisfaction] = useState(85);

  const handleStartSession = async () => {
    setSessionActive(true);
    await startListening();
  };

  const handleEndSession = () => {
    setSessionActive(false);
    stopListening();
  };

  const handleTransferToHuman = () => {
    // Implement human transfer logic
    console.log('Transferring to human agent...');
  };

  useEffect(() => {
    if (currentTranscript && aiResponse) {
      const newEntry = {
        id: Date.now(),
        timestamp: new Date(),
        userInput: currentTranscript,
        aiResponse: aiResponse,
        emotion: emotion,
        confidence: confidence,
        language: language
      };
      
      setConversationHistory(prev => [...prev, newEntry]);
      onConversationUpdate(newEntry);
    }
  }, [currentTranscript, aiResponse, emotion, confidence, language, onConversationUpdate]);

  useEffect(() => {
    if (
      sessionActive &&
      !isListening &&
      !isProcessing &&
      !isSpeaking
    ) {
      // Auto-restart listening for continuous conversation
      startListening();
    }
  }, [sessionActive, isListening, isProcessing, isSpeaking]);

  const getStatusColor = () => {
    if (isListening) return 'bg-green-500';
    if (isProcessing) return 'bg-yellow-500';
    if (isSpeaking) return 'bg-blue-500';
    return 'bg-gray-500';
  };

  const getStatusText = () => {
    if (isListening) return 'Listening to customer...';
    if (isProcessing) return 'AI is thinking...';
    if (isSpeaking) return 'AI is responding...';
    return 'Ready for interaction';
  };

  return (
    <div className="space-y-6">
      {/* Main Voice Control Interface */}
      <Card className="p-6 bg-gradient-to-br from-purple-900 to-blue-900 border-purple-500/30">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-4 h-4 rounded-full animate-pulse ${getStatusColor()}`} />
            <h3 className="text-xl font-bold text-white">AI Voice Agent</h3>
            <Badge variant="outline" className="text-purple-300 border-purple-300">
              {language.toUpperCase()}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Heart className="w-5 h-5 text-red-400" />
            <span className="text-white text-sm">Satisfaction: {customerSatisfaction}%</span>
          </div>
        </div>

        {/* Voice Visualization */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            {/* Outer pulsing rings when active */}
            {sessionActive && (
              <>
                <div className="absolute inset-0 w-32 h-32 rounded-full bg-purple-500/20 animate-ping" />
                <div className="absolute inset-2 w-28 h-28 rounded-full bg-purple-500/30 animate-ping animation-delay-75" />
                <div className="absolute inset-4 w-24 h-24 rounded-full bg-purple-500/40 animate-ping animation-delay-150" />
              </>
            )}
            
            {/* Main control button */}
            <Button
              onClick={sessionActive ? handleEndSession : handleStartSession}
              size="lg"
              className={`w-20 h-20 rounded-full transition-all duration-300 ${
                sessionActive 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {sessionActive ? (
                <Phone className="w-8 h-8" />
              ) : (
                <Mic className="w-8 h-8" />
              )}
            </Button>
          </div>
        </div>

        {/* Status and Audio Levels */}
        <div className="space-y-4">
          <div className="text-center">
            <p className="text-white font-medium">{getStatusText()}</p>
            <p className="text-purple-200 text-sm">
              {sessionActive ? 'Full duplex conversation active' : 'Click to start AI agent'}
            </p>
          </div>

          {sessionActive && (
            <>
              {/* Audio Level Indicator */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-purple-200">Voice Input Level</span>
                  <span className="text-white">{Math.round(audioLevel)}%</span>
                </div>
                <Progress value={audioLevel} className="h-2" />
              </div>

              {/* AI Confidence */}
              {confidence > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-purple-200">AI Confidence</span>
                    <span className="text-white">{Math.round(confidence)}%</span>
                  </div>
                  <Progress value={confidence} className="h-2" />
                </div>
              )}
            </>
          )}
        </div>

        {/* Control Buttons */}
        {sessionActive && (
          <div className="flex justify-center space-x-4 mt-6">
            <Button
              onClick={handleTransferToHuman}
              variant="outline"
              className="text-white border-purple-300 hover:bg-purple-700"
            >
              <Phone className="w-4 h-4 mr-2" />
              Transfer to Human
            </Button>
          </div>
        )}
      </Card>

      {/* Emotion Detection */}
      <EmotionDetector 
        transcript={currentTranscript}
        emotion={emotion}
        onEmotionChange={(newEmotion) => {
          // Handle emotion-based responses
          console.log('Customer emotion detected:', newEmotion);
        }}
      />

      {/* Conversation Flow */}
      <ConversationFlow
        conversationHistory={conversationHistory}
        currentState={conversationState}
        onStateChange={(newState) => {
          console.log('Conversation state changed:', newState);
        }}
      />

      {/* Real-time Transcript Display */}
      {sessionActive && (currentTranscript || aiResponse) && (
        <Card className="p-4 bg-gray-900/50 border-gray-700">
          <div className="space-y-3">
            {currentTranscript && (
              <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <MessageSquare className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 text-sm font-medium">Customer:</span>
                </div>
                <p className="text-white">{currentTranscript}</p>
              </div>
            )}
            
            {aiResponse && (
              <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <Brain className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm font-medium">AI Agent:</span>
                </div>
                <p className="text-white">{aiResponse}</p>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};
