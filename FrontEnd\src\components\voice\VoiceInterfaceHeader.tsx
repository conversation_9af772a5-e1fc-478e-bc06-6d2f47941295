
import React from 'react';
import { Globe } from 'lucide-react';
import { LanguageSelector } from '../language/LanguageSelector';

interface VoiceInterfaceHeaderProps {
  currentLanguage: 'en' | 'de' | 'tr';
  onLanguageChange: (language: 'en' | 'de' | 'tr') => void;
}

export const VoiceInterfaceHeader = ({ 
  currentLanguage, 
  onLanguageChange 
}: VoiceInterfaceHeaderProps) => {
  return (
    <div className="flex items-center justify-between mb-8">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
          <Globe className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-white">Ll-aisolutions Voice AI</h1>
          <p className="text-gray-300">Multilingual AI Voice Assistant</p>
        </div>
      </div>
      
      <LanguageSelector
        currentLanguage={currentLanguage}
        onLanguageChange={onLanguageChange}
      />
    </div>
  );
};
