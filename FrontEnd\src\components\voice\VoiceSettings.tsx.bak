import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { VoiceType, VoiceGender } from '@/pages/Index';
import { Mic } from 'lucide-react';

interface VoiceSettingsProps {
  voiceType: VoiceType;
  voiceGender: VoiceGender;
  onVoiceTypeChange: (type: VoiceType) => void;
  onVoiceGenderChange: (gender: VoiceGender) => void;
}

export const VoiceSettings = ({ 
  voiceType, 
  voiceGender, 
  onVoiceTypeChange, 
  onVoiceGenderChange 
}: VoiceSettingsProps) => {
  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30 shadow-xl">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Mic className="w-5 h-5 mr-2" />
        Voice Settings
      </h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-white text-sm font-medium mb-2">Voice Type</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={() => onVoiceTypeChange('formal')}
              className={`${
                voiceType === 'formal'
                  ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                  : 'bg-white/20 border border-white/30 text-purple hover:bg-white/30 hover:text-white'
              } transition-all duration-300 font-medium`}
            >
              Formal
            </Button>
            <Button
              onClick={() => onVoiceTypeChange('casual')}
              className={`${
                voiceType === 'casual'
                  ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                  : 'bg-white/20 border border-white/30 text-purple hover:bg-white/30 hover:text-white'
              } transition-all duration-300 font-medium`}
            >
              Casual
            </Button>
          </div>
        </div>

        <div>
          <label className="block text-white text-sm font-medium mb-2">Voice Gender</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={voiceGender === 'male' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onVoiceGenderChange('male')}
              className={voiceGender === 'male' 
                  ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                  : 'bg-white/20 border border-white/30 text-purple hover:bg-white/30 hover:text-white'
              }
            >
              Male
            </Button>
            <Button
              variant={voiceGender === 'female' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onVoiceGenderChange('female')}
              className={voiceGender === 'female' 
                  ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                  : 'bg-white/20 border border-white/30 text-purple hover:bg-white/30 hover:text-white'
              }
            >
              Female
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};
