
import { useState, useEffect, useRef, useCallback } from 'react';

interface HuggingFaceConfig {
  apiKey: string;
  endpoints: {
    speechToText: string;
    textGeneration: string;
    textToSpeech: string;
    emotionDetection: string;
    intentClassification: string;
  };
}

interface UseHuggingFaceVoiceEngineProps {
  language: 'en' | 'de' | 'tr';
  industry: string;
  config: HuggingFaceConfig;
}

// Fixed response generation with working models
const generateResponse = async (prompt: string, config: HuggingFaceConfig, context: any) => {
  try {
    console.log('🤖 Generating response for:', prompt);
    
    // Use working Hugging Face models
    const workingModels = [
      'microsoft/DialoGPT-medium',
      'microsoft/DialoGPT-small',
      'facebook/blenderbot-400M-distill'
    ];
    
    for (const model of workingModels) {
      try {
        console.log(`🔄 Trying model: ${model}`);
        const endpoint = `https://api-inference.huggingface.co/models/${model}`;
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputs: prompt,
            parameters: {
              max_new_tokens: 50,
              temperature: 0.7,
              do_sample: true,
              pad_token_id: 50256,
              return_full_text: false
            },
            options: {
              wait_for_model: true,
              use_cache: false
            }
          }),
        });

        console.log(`📡 Response status for ${model}:`, response.status);

        if (response.ok) {
          const result = await response.json();
          console.log('✅ Model response:', result);
          
          if (Array.isArray(result) && result.length > 0) {
            let generatedText = result[0].generated_text || result[0].text;
            
            if (generatedText && generatedText.trim() && generatedText.length > 3) {
              // Clean up the response
              generatedText = generatedText.trim();
              if (generatedText.toLowerCase().startsWith(prompt.toLowerCase().trim())) {
                generatedText = generatedText.substring(prompt.length).trim();
              }
              
              console.log('🎯 Final generated response:', generatedText);
              return generatedText;
            }
          }
        } else {
          const errorText = await response.text();
          console.log(`❌ Model ${model} failed:`, response.status, errorText);
        }
      } catch (error) {
        console.log(`❌ Model ${model} error:`, error);
        continue;
      }
    }
    
    // Enhanced intelligent fallback
    console.log('🔄 Using intelligent fallback');
    return generateIntelligentFallback(prompt, context);
  } catch (error) {
    console.error('❌ Text generation error:', error);
    return generateIntelligentFallback(prompt, context);
  }
};

// Enhanced intelligent responses
const generateIntelligentFallback = (prompt: string, context: any) => {
  const lowerPrompt = prompt.toLowerCase();
  
  // Greeting responses
  if (lowerPrompt.includes('hello') || lowerPrompt.includes('hi') || lowerPrompt.includes('hey')) {
    const greetings = {
      en: [
        "Hello! I'm your AI assistant. How can I help you today?",
        "Hi there! What can I do for you?",
        "Hello! I'm here to assist you. What do you need help with?"
      ],
      de: [
        "Hallo! Ich bin Ihr KI-Assistent. Wie kann ich Ihnen helfen?",
        "Hi! Was kann ich für Sie tun?",
        "Hallo! Ich bin hier, um Ihnen zu helfen. Womit kann ich behilflich sein?"
      ],
      tr: [
        "Merhaba! Ben sizin AI asistanınızım. Size nasıl yardımcı olabilirim?",
        "Selam! Size ne konuda yardımcı olabilirim?",
        "Merhaba! Size yardımcı olmak için buradayım. Neye ihtiyacınız var?"
      ]
    };
    const langGreetings = greetings[context.language as keyof typeof greetings] || greetings.en;
    return langGreetings[Math.floor(Math.random() * langGreetings.length)];
  }
  
  // Question responses
  if (lowerPrompt.includes('can you hear') || lowerPrompt.includes('do you understand') || lowerPrompt.includes('are you able')) {
    const responses = {
      en: [
        "Yes, I can hear you perfectly! How can I assist you today?",
        "I understand you clearly. What would you like to know?",
        "Yes, I'm listening and ready to help. What can I do for you?"
      ],
      de: [
        "Ja, ich kann Sie perfekt hören! Wie kann ich Ihnen heute helfen?",
        "Ich verstehe Sie deutlich. Was möchten Sie wissen?",
        "Ja, ich höre zu und bin bereit zu helfen. Was kann ich für Sie tun?"
      ],
      tr: [
        "Evet, sizi mükemmel bir şekilde duyabiliyorum! Size bugün nasıl yardımcı olabilirim?",
        "Sizi net bir şekilde anlıyorum. Ne öğrenmek istiyorsunuz?",
        "Evet, dinliyorum ve yardım etmeye hazırım. Sizin için ne yapabilirim?"
      ]
    };
    const langResponses = responses[context.language as keyof typeof responses] || responses.en;
    return langResponses[Math.floor(Math.random() * langResponses.length)];
  }
  
  // Help requests
  if (lowerPrompt.includes('help') || lowerPrompt.includes('assist')) {
    const responses = {
      en: [
        "I'm here to help! What specific assistance do you need?",
        "Of course! What can I help you with today?",
        "I'd be happy to assist you. What do you need help with?"
      ],
      de: [
        "Ich bin hier, um zu helfen! Welche spezielle Hilfe benötigen Sie?",
        "Natürlich! Womit kann ich Ihnen heute helfen?",
        "Gerne helfe ich Ihnen. Wobei brauchen Sie Hilfe?"
      ],
      tr: [
        "Yardım etmek için buradayım! Hangi konuda özel yardıma ihtiyacınız var?",
        "Tabii ki! Bugün size neyle yardımcı olabilirim?",
        "Size yardımcı olmaktan mutluluk duyarım. Neye ihtiyacınız var?"
      ]
    };
    const langResponses = responses[context.language as keyof typeof responses] || responses.en;
    return langResponses[Math.floor(Math.random() * langResponses.length)];
  }
  
  // Default contextual response
  const defaults = {
    en: [
      `I understand you're asking about "${prompt}". Could you provide more details so I can help you better?`,
      `That's an interesting question. How can I assist you with this?`,
      `I'd be happy to help you. What specific information are you looking for?`
    ],
    de: [
      `Ich verstehe, dass Sie nach "${prompt}" fragen. Können Sie weitere Details angeben?`,
      `Das ist eine interessante Frage. Wie kann ich Ihnen dabei helfen?`,
      `Gerne helfe ich Ihnen. Welche spezifischen Informationen suchen Sie?`
    ],
    tr: [
      `"${prompt}" hakkında soru sorduğunuzu anlıyorum. Daha fazla detay verebilir misiniz?`,
      `İlginç bir soru. Bu konuda size nasıl yardımcı olabilirim?`,
      `Size yardımcı olmaktan mutluluk duyarım. Hangi özel bilgileri arıyorsunuz?`
    ]
  };
  const langDefaults = defaults[context.language as keyof typeof defaults] || defaults.en;
  return langDefaults[Math.floor(Math.random() * langDefaults.length)];
};

// Improved transcription
const transcribeAudio = async (audioBlob: Blob, config: HuggingFaceConfig) => {
  try {
    console.log('🎤 Starting transcription with blob size:', audioBlob.size);
    
    const endpoint = config.endpoints.speechToText || 'https://api-inference.huggingface.co/models/openai/whisper-large-v3';
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: audioBlob,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Speech-to-text error:', errorText);
      throw new Error(`Speech-to-text API call failed: ${response.status}`);
    }

    const result = await response.json();
    console.log('🎯 Transcription result:', result);
    
    return result.text || result || '';
  } catch (error) {
    console.error('❌ Speech-to-text error:', error);
    throw error;
  }
};

// Enhanced browser TTS
const synthesizeSpeech = async (text: string, config: HuggingFaceConfig, language: string = 'en') => {
  return new Promise<boolean>((resolve) => {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
      
      const utterance = new SpeechSynthesisUtterance(text);
      
      const languageCodes = {
        'en': 'en-US',
        'de': 'de-DE',
        'tr': 'tr-TR'
      };
      
      utterance.lang = languageCodes[language as keyof typeof languageCodes] || 'en-US';
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;
      
      // Better voice selection
      const voices = speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => {
        const voiceLang = utterance.lang.substring(0, 2);
        return voice.lang.startsWith(voiceLang) && voice.localService;
      });
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }
      
      utterance.onend = () => {
        console.log('🔊 Speech synthesis completed');
        resolve(true);
      };
      
      utterance.onerror = (event) => {
        console.error('❌ Speech synthesis error:', event);
        resolve(false);
      };
      
      console.log('🔊 Starting speech synthesis for:', text);
      speechSynthesis.speak(utterance);
    } else {
      console.error('❌ Speech synthesis not supported');
      resolve(false);
    }
  });
};

export const useHuggingFaceVoiceEngine = ({ language, industry, config }: UseHuggingFaceVoiceEngineProps) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [confidence, setConfidence] = useState(0);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [emotion, setEmotion] = useState('neutral');
  const [intent, setIntent] = useState('');
  const [conversationState, setConversationState] = useState('greeting');
  const [contextMemory, setContextMemory] = useState<any[]>([]);
  const [sessionActive, setSessionActive] = useState(false);
  const [fullDuplexMode, setFullDuplexMode] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const processingRef = useRef(false);
  const voiceActivityRef = useRef(false);
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const conversationIdRef = useRef<string>('');

  // Enhanced Voice Activity Detection
  const detectVoiceActivity = useCallback((analyser: AnalyserNode) => {
    const dataArray = new Uint8Array(analyser.frequencyBinCount);
    analyser.getByteFrequencyData(dataArray);
    const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
    const level = (average / 255) * 100;
    
    setAudioLevel(level);
    
    const isActive = level > 20;
    
    if (isActive !== voiceActivityRef.current) {
      voiceActivityRef.current = isActive;
      console.log('🎙️ Voice activity:', isActive ? 'ACTIVE' : 'INACTIVE', 'Level:', level);
      
      if (isActive) {
        if (silenceTimeoutRef.current) {
          clearTimeout(silenceTimeoutRef.current);
          silenceTimeoutRef.current = null;
        }
      } else {
        if (!silenceTimeoutRef.current && fullDuplexMode && !processingRef.current) {
          silenceTimeoutRef.current = setTimeout(() => {
            console.log('🔇 Silence detected - stopping recording');
            if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
              mediaRecorderRef.current.stop();
            }
          }, 1500);
        }
      }
    }
    
    return level;
  }, [fullDuplexMode]);

  const startListening = useCallback(async () => {
    try {
      if (!config.apiKey) {
        console.error('❌ Hugging Face API key is required');
        return;
      }

      if (processingRef.current) {
        console.log('⏭️ Already processing, skipping...');
        return;
      }

      console.log('🎤 Starting voice recording...');
      setIsListening(true);
      setCurrentTranscript('');
      setAiResponse('');
      voiceActivityRef.current = false;

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000
        } 
      });
      streamRef.current = stream;

      audioContextRef.current = new AudioContext({ sampleRate: 16000 });
      const source = audioContextRef.current.createMediaStreamSource(stream);
      const analyser = audioContextRef.current.createAnalyser();
      analyser.fftSize = 512;
      analyser.minDecibels = -90;
      analyser.maxDecibels = -10;
      source.connect(analyser);

      const monitorVoiceActivity = () => {
        if (isListening && audioContextRef.current && analyser) {
          detectVoiceActivity(analyser);
          requestAnimationFrame(monitorVoiceActivity);
        }
      };
      monitorVoiceActivity();

      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        if (processingRef.current) {
          console.log('⏭️ Already processing, ignoring stop event');
          return;
        }
        
        console.log('⏹️ Recording stopped, processing audio...');
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm;codecs=opus' });
        audioChunksRef.current = [];
        
        if (audioBlob.size > 5000) {
          await processAudio(audioBlob);
        } else {
          console.log('📏 Audio too short, restarting listening...');
          setIsListening(false);
          if (fullDuplexMode && sessionActive) {
            setTimeout(() => startListening(), 1000);
          }
        }
      };

      mediaRecorderRef.current.start(100);
      console.log('✅ Recording started');

      // Auto-stop after 10 seconds
      setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          console.log('⏰ Auto-stopping recording after 10 seconds');
          mediaRecorderRef.current.stop();
        }
      }, 10000);

    } catch (error) {
      console.error('❌ Failed to start listening:', error);
      setIsListening(false);
      processingRef.current = false;
      if (fullDuplexMode && sessionActive) {
        setTimeout(() => startListening(), 2000);
      }
    }
  }, [isListening, config, fullDuplexMode, sessionActive, detectVoiceActivity]);

  const stopListening = useCallback(() => {
    console.log('🛑 Stopping voice recording...');
    setIsListening(false);
    setAudioLevel(0);
    voiceActivityRef.current = false;

    // Clear all timeouts
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }

    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
  }, []);

  const processAudio = async (audioBlob: Blob) => {
    if (processingRef.current) {
      console.log('⏭️ Already processing audio, skipping...');
      return;
    }
    
    console.log('🔄 Setting processing state...');
    processingRef.current = true;
    setIsProcessing(true);
    setIsListening(false);
    
    try {
      console.log('🎵 Processing audio blob:', audioBlob.size, 'bytes');
      
      const wavBlob = await convertToWav(audioBlob);
      const transcript = await transcribeAudio(wavBlob, config);
      console.log('📝 Transcription result:', transcript);
      
      if (transcript && transcript.trim() && transcript.length > 2) {
        setCurrentTranscript(transcript);
        setConfidence(95);

        const detectedIntent = detectIntent(transcript);
        setIntent(detectedIntent);

        const enhancedContext = { 
          language, 
          industry, 
          conversationHistory: contextMemory,
          userIntent: detectedIntent,
          currentEmotion: emotion
        };
        
        const response = await generateResponse(transcript, config, enhancedContext);
        setAiResponse(response);

        // Update conversation memory
        const conversationId = `${Date.now()}-${Math.random()}`;
        if (conversationId !== conversationIdRef.current) {
          conversationIdRef.current = conversationId;
          
          const newMemoryEntry = {
            user: transcript,
            ai: response,
            emotion: 'neutral',
            intent: detectedIntent,
            timestamp: Date.now(),
            industry: industry,
            language: language
          };
          
          setContextMemory(prev => [...prev.slice(-2), newMemoryEntry]);
        }

        console.log('🔊 Starting speech synthesis...');
        setIsSpeaking(true);
        await synthesizeSpeech(response, config, language);
        setIsSpeaking(false);
        console.log('✅ Speech synthesis completed');

        // Reset processing state
        setTimeout(() => {
          console.log('🔄 Resetting processing state...');
          processingRef.current = false;
          setIsProcessing(false);
          
          if (fullDuplexMode && sessionActive) {
            console.log('🔄 Auto-restarting listening...');
            setTimeout(() => startListening(), 1000);
          }
        }, 500);

      } else {
        console.log('❌ No meaningful transcript, restarting...');
        processingRef.current = false;
        setIsProcessing(false);
        
        if (fullDuplexMode && sessionActive) {
          setTimeout(() => startListening(), 1000);
        }
      }
      
    } catch (error) {
      console.error('❌ Audio processing error:', error);
      processingRef.current = false;
      setIsProcessing(false);
      
      if (fullDuplexMode && sessionActive) {
        setTimeout(() => startListening(), 2000);
      }
    }
  };

  // Convert audio to WAV format
  const convertToWav = async (audioBlob: Blob): Promise<Blob> => {
    try {
      const audioContext = new AudioContext({ sampleRate: 16000 });
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      const wavArrayBuffer = audioBufferToWav(audioBuffer);
      return new Blob([wavArrayBuffer], { type: 'audio/wav' });
    } catch (error) {
      console.log('🔄 Audio conversion failed, using original:', error);
      return audioBlob;
    }
  };

  // Convert AudioBuffer to WAV format
  const audioBufferToWav = (buffer: AudioBuffer): ArrayBuffer => {
    const length = buffer.length;
    const sampleRate = buffer.sampleRate;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    const channelData = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
    
    return arrayBuffer;
  };

  const detectIntent = (text: string): string => {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('hello') || lowerText.includes('hi') || lowerText.includes('hey')) {
      return 'greeting';
    } else if (lowerText.includes('help') || lowerText.includes('support')) {
      return 'help_request';
    } else if (lowerText.includes('problem') || lowerText.includes('issue')) {
      return 'problem_report';
    } else if (lowerText.includes('thank') || lowerText.includes('bye')) {
      return 'ending';
    } else {
      return 'general_inquiry';
    }
  };

  // Start full duplex mode
  const startFullDuplexMode = useCallback(async () => {
    console.log('🚀 Starting full duplex mode...');
    setFullDuplexMode(true);
    setSessionActive(true);
    processingRef.current = false;
    conversationIdRef.current = '';
    await startListening();
  }, [startListening]);

  // Stop full duplex mode
  const stopFullDuplexMode = useCallback(() => {
    console.log('🛑 Stopping full duplex mode...');
    setFullDuplexMode(false);
    setSessionActive(false);
    stopListening();
    processingRef.current = false;
    conversationIdRef.current = '';
  }, [stopListening]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      stopListening();
      processingRef.current = false;
      conversationIdRef.current = '';
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }
    };
  }, [stopListening]);

  return {
    isListening,
    isProcessing,
    isSpeaking,
    audioLevel,
    confidence,
    currentTranscript,
    aiResponse,
    emotion,
    intent,
    conversationState,
    contextMemory,
    sessionActive,
    fullDuplexMode,
    startListening,
    stopListening,
    startFullDuplexMode,
    stopFullDuplexMode,
    synthesizeSpeech: (text: string) => synthesizeSpeech(text, config, language)
  };
};
