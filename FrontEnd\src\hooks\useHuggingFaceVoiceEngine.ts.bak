import { useState, useEffect, useRef, useCallback } from 'react';

interface HuggingFaceConfig {
  apiKey: string;
  endpoints: {
    speechToText: string;
    textGeneration: string;
    textToSpeech: string;
    emotionDetection: string;
    intentClassification: string;
  };
}

interface UseHuggingFaceVoiceEngineProps {
  language: 'en' | 'de' | 'tr';
  industry: string;
  config: HuggingFaceConfig;
}

// Helper function to make Hugging Face API calls
const callHuggingFaceAPI = async (endpoint: string, apiKey: string, data: any) => {
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }

  return await response.json();
};

// Speech-to-text function - Fixed for Hugging Face API
const transcribeAudio = async (audioBlob: Blob, config: HuggingFaceConfig) => {
  try {
    console.log('Starting transcription with blob size:', audioBlob.size);
    
    // Use default working endpoint if not configured
    const endpoint = config.endpoints.speechToText || 'https://api-inference.huggingface.co/models/openai/whisper-large-v3';
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'audio/wav',
      },
      body: audioBlob,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Speech-to-text error response:', errorText);
      throw new Error(`Speech-to-text API call failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Transcription result:', result);
    
    return result.text || result || '';
  } catch (error) {
    console.error('Speech-to-text error:', error);
    throw error;
  }
};

// Text generation function with working models
const generateResponse = async (prompt: string, config: HuggingFaceConfig, context: any) => {
  try {
    // Create a system prompt to guide the AI's behavior and language
    const systemPrompt = `You are a helpful AI assistant for ${context.industry} industry. Respond in ${context.language}. Be concise and helpful.`;
    
    // List of fallback Hugging Face endpoints for text generation
    const workingEndpoints = [
      'https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium',
      'https://api-inference.huggingface.co/models/facebook/blenderbot-400M-distill',
      'https://api-inference.huggingface.co/models/google/flan-t5-base'
    ];
    
    // Use the configured endpoint or the first fallback as default
    const endpoint = config.endpoints.textGeneration || workingEndpoints[0];
    
    let result;
    
    // Try the configured endpoint first
    try {
      // Call Hugging Face API with the system prompt and user input
      result = await callHuggingFaceAPI(endpoint, config.apiKey, {
        inputs: `${systemPrompt}\n\nUser: ${prompt}\nAssistant:`,
        parameters: {
          max_new_tokens: 100,           // Limit the response length
          temperature: 0.7,              // Controls randomness/creativity
          return_full_text: false,       // Only return the new text
          do_sample: true                // Enable sampling for more natural responses
        }
      });
    } catch (error) {
      // If the primary endpoint fails, try the fallback endpoints
      console.log('Primary endpoint failed, trying fallback...');
      
      // Try each fallback endpoint in order
      for (const fallbackEndpoint of workingEndpoints) {
        try {
          result = await callHuggingFaceAPI(fallbackEndpoint, config.apiKey, {
            inputs: prompt,
            parameters: {
              max_length: 100,
              temperature: 0.7
            }
          });
          break; // Stop trying if one succeeds
        } catch (fallbackError) {
          // If a fallback also fails, continue to the next one
          console.log(`Fallback endpoint ${fallbackEndpoint} also failed`);
          continue;
        }
      }
    }

    // Log the result for debugging
    console.log('Text generation result:', result);
    
    // Return the generated text from the API response, or a default message
    if (Array.isArray(result) && result.length > 0) {
      return result[0].generated_text || result[0].response || 'Yes, I can hear you! How can I help you today?';
    } else if (result.generated_text) {
      return result.generated_text;
    } else {
      return 'Yes, I can hear you! How can I help you today?';
    }
  } catch (error) {
    // If all attempts fail, log the error and return a default message
    console.error('Text generation error:', error);
    return 'Yes, I can hear you! How can I help you today?';
  }
};

// Emotion detection function with fallback
const detectEmotion = async (text: string, config: HuggingFaceConfig) => {
  try {
    // Use working emotion detection model
    const endpoint = config.endpoints.emotionDetection || 'https://api-inference.huggingface.co/models/j-hartmann/emotion-english-distilroberta-base';
    
    const result = await callHuggingFaceAPI(endpoint, config.apiKey, {
      inputs: text
    });

    console.log('Emotion detection result:', result);

    if (Array.isArray(result) && result.length > 0) {
      const emotions = result[0];
      if (Array.isArray(emotions)) {
        const topEmotion = emotions.reduce((prev, current) => 
          (prev.score > current.score) ? prev : current
        );
        return topEmotion.label;
      }
    }
    
    return 'neutral';
  } catch (error) {
    console.error('Emotion detection error:', error);
    return 'neutral';
  }
};

// Text-to-speech function with browser fallback
const synthesizeSpeech = async (text: string, config: HuggingFaceConfig) => {
  try {
    // Try Hugging Face TTS first
    const endpoint = config.endpoints.textToSpeech || 'https://api-inference.huggingface.co/models/espnet/kan-bayashi_ljspeech_vits';
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: text
      }),
    });

    if (response.ok) {
      const audioBlob = await response.blob();
      
      // Play the audio
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);
      
      return new Promise((resolve) => {
        audio.onended = () => {
          URL.revokeObjectURL(audioUrl);
          resolve(true);
        };
        audio.onerror = () => {
          URL.revokeObjectURL(audioUrl);
          // Fallback to browser speech synthesis
          useBrowserTTS(text);
          resolve(false);
        };
        audio.play();
      });
    } else {
      throw new Error('TTS API failed');
    }
  } catch (error) {
    console.error('Text-to-speech error:', error);
    // Fallback to browser speech synthesis
    useBrowserTTS(text);
    return false;
  }
};

// Browser TTS fallback
const useBrowserTTS = (text: string) => {
  if ('speechSynthesis' in window) {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'en-US';
    utterance.rate = 0.9;
    utterance.pitch = 1;
    speechSynthesis.speak(utterance);
  }
};

export const useHuggingFaceVoiceEngine = ({ language, industry, config }: UseHuggingFaceVoiceEngineProps) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [confidence, setConfidence] = useState(0);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [emotion, setEmotion] = useState('neutral');
  const [intent, setIntent] = useState('');
  const [conversationState, setConversationState] = useState('greeting');
  const [contextMemory, setContextMemory] = useState<any[]>([]);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const startListening = useCallback(async () => {
    try {
      if (!config.apiKey) {
        console.error('Hugging Face API key is required');
        return;
      }

      console.log('Starting voice recording...');
      setIsListening(true);
      setCurrentTranscript('');
      setAiResponse('');

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000
        } 
      });
      streamRef.current = stream;

      // Initialize audio context for level monitoring
      audioContextRef.current = new AudioContext({ sampleRate: 16000 });
      const source = audioContextRef.current.createMediaStreamSource(stream);
      const analyser = audioContextRef.current.createAnalyser();
      source.connect(analyser);

      // Monitor audio levels
      const monitorAudioLevel = () => {
        if (isListening && audioContextRef.current) {
          const dataArray = new Uint8Array(analyser.frequencyBinCount);
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
          setAudioLevel((average / 255) * 100);
          requestAnimationFrame(monitorAudioLevel);
        }
      };
      monitorAudioLevel();

      // Set up MediaRecorder
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        console.log('Recording stopped, processing audio...');
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm;codecs=opus' });
        audioChunksRef.current = [];
        
        if (audioBlob.size > 0) {
          await processAudio(audioBlob);
        }
      };

      // Start recording
      mediaRecorderRef.current.start();
      console.log('Recording started');

      // Auto-stop after 5 seconds for demo purposes
      setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          console.log('Auto-stopping recording after 5 seconds');
          mediaRecorderRef.current.stop();
        }
      }, 5000);

    } catch (error) {
      console.error('Failed to start listening:', error);
      setIsListening(false);
    }
  }, [isListening, config]);

  const stopListening = useCallback(() => {
    console.log('Stopping voice recording...');
    setIsListening(false);
    setAudioLevel(0);

    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
  }, []);

  const processAudio = async (audioBlob: Blob) => {
    setIsProcessing(true);
    
    try {
      console.log('Processing audio blob:', audioBlob.size, 'bytes');
      
      // Convert to WAV format for better compatibility
      const wavBlob = await convertToWav(audioBlob);
      
      // Transcribe audio
      const transcript = await transcribeAudio(wavBlob, config);
      console.log('Transcription result:', transcript);
      
      if (transcript && transcript.trim()) {
        setCurrentTranscript(transcript);
        setConfidence(85); // Mock confidence for demo

        // Detect emotion
        const detectedEmotion = await detectEmotion(transcript, config);
        setEmotion(detectedEmotion);

        // Simple intent detection
        const detectedIntent = detectIntent(transcript);
        setIntent(detectedIntent);

        // Generate AI response
        const context = { language, industry, emotion: detectedEmotion, conversationState };
        const response = await generateResponse(transcript, config, context);
        setAiResponse(response);

        // Update conversation memory
        const newMemoryEntry = {
          user: transcript,
          ai: response,
          emotion: detectedEmotion,
          intent: detectedIntent,
          timestamp: Date.now()
        };
        setContextMemory(prev => [...prev.slice(-4), newMemoryEntry]);

        // Synthesize speech response
        setIsSpeaking(true);
        await synthesizeSpeech(response, config);
        setIsSpeaking(false);

      } else {
        console.log('No transcript received or empty transcript');
        setCurrentTranscript('Could not understand audio. Please try again.');
      }
      
    } catch (error) {
      console.error('Audio processing error:', error);
      setCurrentTranscript('Error processing audio. Please try again.');
    } finally {
      setIsProcessing(false);
      setIsListening(false);
    }
  };

  // Convert audio to WAV format for better compatibility
  const convertToWav = async (audioBlob: Blob): Promise<Blob> => {
    try {
      const audioContext = new AudioContext({ sampleRate: 16000 });
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      // Convert to WAV
      const wavArrayBuffer = audioBufferToWav(audioBuffer);
      return new Blob([wavArrayBuffer], { type: 'audio/wav' });
    } catch (error) {
      console.log('Audio conversion failed, using original:', error);
      return audioBlob;
    }
  };

  // Convert AudioBuffer to WAV format
  const audioBufferToWav = (buffer: AudioBuffer): ArrayBuffer => {
    const length = buffer.length;
    const sampleRate = buffer.sampleRate;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    
    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Convert audio data
    const channelData = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
    
    return arrayBuffer;
  };

  const detectIntent = (text: string): string => {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('hello') || lowerText.includes('hi') || lowerText.includes('hey')) {
      return 'greeting';
    } else if (lowerText.includes('help') || lowerText.includes('support') || lowerText.includes('assist')) {
      return 'help_request';
    } else if (lowerText.includes('problem') || lowerText.includes('issue') || lowerText.includes('error')) {
      return 'problem_report';
    } else if (lowerText.includes('thank') || lowerText.includes('bye') || lowerText.includes('goodbye')) {
      return 'ending';
    } else {
      return 'general_inquiry';
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      stopListening();
    };
  }, [stopListening]);

  return {
    isListening,
    isProcessing,
    isSpeaking,
    audioLevel,
    confidence,
    currentTranscript,
    aiResponse,
    emotion,
    intent,
    conversationState,
    contextMemory,
    startListening,
    stopListening,
    synthesizeSpeech: (text: string) => synthesizeSpeech(text, config)
  };
};
