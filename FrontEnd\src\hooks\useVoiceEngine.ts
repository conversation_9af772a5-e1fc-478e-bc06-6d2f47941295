import { useState, useEffect, useRef, useCallback } from 'react';

// Interface for optional API endpoints and keys
interface VoiceEngineConfig {
  sttApiUrl?: string;
  sttApiKey?: string;
  llmApiUrl?: string;
  llmApiKey?: string;
  ttsApiUrl?: string;
  ttsApiKey?: string;
  emotionApiUrl?: string;
  emotionApiKey?: string;
}

interface UseVoiceEngineProps {
  language: 'en' | 'de' | 'tr';
  config?: VoiceEngineConfig;
}

export const useVoiceEngine = ({ language, config }: UseVoiceEngineProps) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [confidence, setConfidence] = useState(0);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [emotion, setEmotion] = useState<string>('neutral');
  const [conversationState, setConversationState] = useState('greeting');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Helper to get browser language code
  const getLanguageCode = (lang: string) => {
    const codes = {
      'en': 'en-US',
      'de': 'de-DE',
      'tr': 'tr-TR'
    };
    return codes[lang as keyof typeof codes] || 'en-US';
  };

  // Start listening and recording audio
  const startListening = useCallback(async () => {
    if (isSpeaking || isListening) return;
    try {
      setIsListening(true);
      setCurrentTranscript('');
      setAiResponse('');
      audioChunksRef.current = [];

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      const analyser = audioContextRef.current.createAnalyser();
      source.connect(analyser);

      // Monitor audio input level for visualization
      const monitorAudioLevel = () => {
        if (isListening) {
          const dataArray = new Uint8Array(analyser.frequencyBinCount);
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b, 0) / dataArray.length;
          setAudioLevel((average / 255) * 100);
          requestAnimationFrame(monitorAudioLevel);
        }
      };
      monitorAudioLevel();

      // Setup MediaRecorder for audio capture
      const mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/webm' });
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        setIsListening(false);
        setAudioLevel(0);
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        audioChunksRef.current = [];
        if (audioBlob.size > 1000) {
          await processAudioWithSTT(audioBlob);
        }
      };

      mediaRecorder.start();

      // Auto-stop after 7 seconds or when you want
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
      }, 7000);

    } catch (error) {
      console.error('Failed to start listening:', error);
      setIsListening(false);
    }
  }, [isListening, isSpeaking]);

  // Stop listening and clean up resources
  const stopListening = useCallback(() => {
    setIsListening(false);
    setAudioLevel(0);

    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
  }, []);

  // Convert webm audio to wav (for Whisper API compatibility)
  const convertWebmToWav = async (webmBlob: Blob): Promise<Blob> => {
    // For simplicity, send webm directly (Whisper API accepts webm/ogg/wav/mp3/m4a)
    return webmBlob;
  };

  // Process audio with Hugging Face Whisper STT API
  const processAudioWithSTT = async (audioBlob: Blob) => {
    setIsProcessing(true);
    try {
      const wavBlob = await convertWebmToWav(audioBlob);
      const response = await fetch(
        config?.sttApiUrl || 'https://api-inference.huggingface.co/models/openai/whisper-large-v3',
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config?.sttApiKey || '*************************************'}`
          },
          body: wavBlob
        }
      );
      const data = await response.json();
      const transcript = data.text || '';
      setCurrentTranscript(transcript);
      setConfidence(95); // Whisper does not return confidence, so set a default

      if (transcript && transcript.length > 0) {
        await processWithAI(transcript);
      }
    } catch (error) {
      console.error('STT error:', error);
      setCurrentTranscript('');
      setAiResponse(getFallbackResponse(language));
      await synthesizeSpeech(getFallbackResponse(language));
    } finally {
      setIsProcessing(false);
    }
  };

  // Process transcript with AI (emotion, LLM, TTS)
  const processWithAI = useCallback(async (transcript: string) => {
    setIsProcessing(true);
    try {
      // Detect emotion (Hugging Face API)
      const detectedEmotion = await detectEmotion(transcript);
      setEmotion(detectedEmotion);

      // Get AI response (Hugging Face LLM API)
      const response = await processWithLLM(transcript, detectedEmotion, language, conversationState);
      setAiResponse(response.text);
      setConversationState(response.nextState || conversationState);

      // Speak the AI response using Hugging Face TTS
      await synthesizeSpeech(response.text);

    } catch (error) {
      console.error('AI processing error:', error);
      const fallbackResponse = getFallbackResponse(language);
      setAiResponse(fallbackResponse);
      await synthesizeSpeech(fallbackResponse);
    } finally {
      setIsProcessing(false);
    }
  }, [language, conversationState]);

  // Emotion detection using Hugging Face API
  const detectEmotion = async (text: string): Promise<string> => {
    try {
      const response = await fetch(
        config?.emotionApiUrl || 'https://api-inference.huggingface.co/models/boltuix/bert-emotion',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config?.emotionApiKey || '*************************************'}`
          },
          body: JSON.stringify({ inputs: text })
        }
      );
      const data = await response.json();
      if (Array.isArray(data) && data.length > 0 && data[0].label) {
        return data[0].label;
      }
      if (data.labels && data.labels.length > 0) {
        return data.labels[0];
      }
      return 'neutral';
    } catch (error) {
      console.error('Emotion detection error:', error);
      return 'neutral';
    }
  };

  // LLM/AI integration using Hugging Face API
  const processWithLLM = async (
    transcript: string,
    emotion: string,
    language: string,
    state: string
  ) => {
    try {
      const response = await fetch(
        config?.llmApiUrl || 'https://api-inference.huggingface.co/models/mistralai/Mistral-7B-Instruct-v0.3',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config?.llmApiKey || '*************************************'}`
          },
          body: JSON.stringify({
            inputs: `User: ${transcript}\nEmotion: ${emotion}\nLanguage: ${language}\nState: ${state}\nAI:`
          })
        }
      );
      const data = await response.json();
      if (Array.isArray(data) && data.length > 0 && data[0].generated_text) {
        return {
          text: data[0].generated_text,
          nextState: state
        };
      }
      return {
        text: "Sorry, I couldn't process that.",
        nextState: state
      };
    } catch (error) {
      console.error('LLM API error:', error);
      return {
        text: "Sorry, I couldn't process that.",
        nextState: state
      };
    }
  };

  // Hugging Face TTS: synthesize speech and play audio
  const synthesizeSpeech = useCallback(async (text: string) => {
    setIsSpeaking(true);
    try {
      const response = await fetch(
        config?.ttsApiUrl || 'https://api-inference.huggingface.co/models/coqui/XTTS-v2',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config?.ttsApiKey || '*************************************'}`
          },
          body: JSON.stringify({
            inputs: text,
            options: { wait_for_model: true }
          })
        }
      );
      if (!response.ok) throw new Error('TTS API error');
      const data = await response.json();
      // Hugging Face TTS returns a base64-encoded wav/mp3 in 'audio' or 'generated_audio'
      const audioBase64 = data.audio || data.generated_audio;
      if (audioBase64) {
        const audioBlob = b64toBlob(audioBase64, 'audio/wav');
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        audio.onended = () => {
          setIsSpeaking(false);
          URL.revokeObjectURL(audioUrl);
        };
        audio.play();
      } else {
        setIsSpeaking(false);
      }
    } catch (error) {
      console.error('Speech synthesis error:', error);
      setIsSpeaking(false);
    }
  }, [language, config]);

  // Helper: Convert base64 to Blob
  function b64toBlob(b64Data: string, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    return new Blob(byteArrays, { type: contentType });
  }

  // Fallback response if AI fails
  const getFallbackResponse = (language: string): string => {
    const fallbacks = {
      en: "I apologize, but I'm having trouble processing your request. Could you please try again?",
      de: "Entschuldigung, ich habe Probleme bei der Bearbeitung Ihrer Anfrage. Könnten Sie es bitte noch einmal versuchen?",
      tr: "Özür dilerim, isteğinizi işlemekte sorun yaşıyorum. Lütfen tekrar deneyebilir misiniz?"
    };
    return fallbacks[language as keyof typeof fallbacks] || fallbacks.en;
  };

  // Auto-restart listening after TTS finishes
  useEffect(() => {
    if (!isSpeaking && !isListening && !isProcessing) {
      startListening();
    }
  }, [isSpeaking, isListening, isProcessing, startListening]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopListening();
    };
  }, [stopListening]);

  return {
    isListening,
    isProcessing,
    isSpeaking,
    audioLevel,
    confidence,
    currentTranscript,
    aiResponse,
    emotion,
    conversationState,
    startListening,
    stopListening,
    processWithAI,
    synthesizeSpeech
  };
};

const config = {
  sttApiUrl: "https://api-inference.huggingface.co/models/openai/whisper-large-v3",
  sttApiKey: "*************************************",
  llmApiUrl: "https://api-inference.huggingface.co/models/mistralai/Mistral-7B-Instruct-v0.3",
  llmApiKey: "*************************************",
  ttsApiUrl: "https://api-inference.huggingface.co/models/coqui/XTTS-v2",
  ttsApiKey: "*************************************",
  emotionApiUrl: "https://api-inference.huggingface.co/models/boltuix/bert-emotion",
  emotionApiKey: "*************************************"
};

const voiceEngine = useVoiceEngine({ language: 'en', config });
