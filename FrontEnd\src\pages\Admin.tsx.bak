
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { ArrowLeft, LogIn } from 'lucide-react';
import { Link } from 'react-router-dom';
import { AdminHeader } from '@/components/admin/AdminHeader';
import { EnhancedStatsCards } from '@/components/admin/EnhancedStatsCards';
import { EnhancedComplianceStatus } from '@/components/admin/EnhancedComplianceStatus';
import { DataHandlingOverview } from '@/components/admin/DataHandlingOverview';
import { NavigationTabs } from '@/components/admin/NavigationTabs';
import { CallLogsTable } from '@/components/admin/CallLogsTable';
import { AdvancedAnalytics } from '@/components/admin/AdvancedAnalytics';
import { AuditTrail } from '@/components/admin/AuditTrail';
import { DashboardWidgets } from '@/components/admin/DashboardWidgets';

const Admin = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (username === 'admin' && password === 'admin') {
      setIsLoggedIn(true);
    } else {
      alert('Demo credentials: admin/admin');
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-500 via-purple-600 to-blue-700 flex items-center justify-center">
        <Card className="w-full max-w-md p-6 bg-white/20 backdrop-blur-sm border-white/30">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-white">Admin Login</h1>
            <p className="text-white/80 mt-2">Access the administrative dashboard</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <Label htmlFor="username" className="text-white">Username</Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter username"
                required
                className="bg-white/20 border-white/30 text-white placeholder:text-white/60"
              />
            </div>
            <div>
              <Label htmlFor="password" className="text-white">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
                required
                className="bg-white/20 border-white/30 text-white placeholder:text-white/60"
              />
            </div>
            <Button type="submit" className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
              <LogIn className="w-4 h-4 mr-2" />
              Login
            </Button>
          </form>

          <div className="mt-6 text-center">
            <Link to="/">
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Voice Agent
              </Button>
            </Link>
          </div>

          <div className="mt-4 p-3 bg-white/20 rounded-lg border border-white/30">
            <p className="text-sm text-white">
              <strong>Demo credentials:</strong><br />
              Username: admin<br />
              Password: admin
            </p>
          </div>
        </Card>
      </div>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-8">
            <EnhancedStatsCards />
            <DashboardWidgets />
            <div className="grid lg:grid-cols-1 gap-8">
              <CallLogsTable />
            </div>
          </div>
        );
      case 'logs':
        return (
          <div className="space-y-8">
            <CallLogsTable />
            <AuditTrail />
          </div>
        );
      case 'metrics':
        return <AdvancedAnalytics />;
      case 'compliance':
        return (
          <div className="space-y-8">
            <EnhancedComplianceStatus />
            <DataHandlingOverview />
            <AuditTrail />
          </div>
        );
      case 'settings':
        return (
          <div className="space-y-8">
            <Card className="p-6 bg-gray-800/50 border-gray-700">
              <h3 className="text-xl font-semibold text-white mb-4">System Configuration</h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-key" className="text-gray-300">API Key</Label>
                  <Input
                    id="api-key"
                    type="password"
                    value="sk-••••••••••••••••••••"
                    readOnly
                    className="bg-gray-900 border-gray-700 text-gray-300"
                  />
                </div>
                <div>
                  <Label htmlFor="webhook-url" className="text-gray-300">Webhook URL</Label>
                  <Input
                    id="webhook-url"
                    value="https://api.ll-aisolutions.com/webhooks/voice"
                    readOnly
                    className="bg-gray-900 border-gray-700 text-gray-300"
                  />
                </div>
                <div>
                  <Label htmlFor="max-concurrent" className="text-gray-300">Max Concurrent Calls</Label>
                  <Input
                    id="max-concurrent"
                    type="number"
                    value="100"
                    className="bg-gray-900 border-gray-700 text-gray-300"
                  />
                </div>
              </div>
            </Card>
            <AuditTrail />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      <AdminHeader onLogout={() => setIsLoggedIn(false)} />

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <NavigationTabs activeTab={activeTab} onTabChange={setActiveTab} />
        {renderTabContent()}
      </main>
    </div>
  );
};

export default Admin;
