
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { EnhancedVoiceEngineController } from '@/components/voice/EnhancedVoiceEngineController';
import { LanguageSelector } from '@/components/language/LanguageSelector';
import { ConversationIntelligence } from '@/components/user/ConversationIntelligence';
import { CallManagement } from '@/components/user/CallManagement';
import { RealTimeInteractionFeedback } from '@/components/user/RealTimeInteractionFeedback';
import { AIBehaviorSettings } from '@/components/user/AIBehaviorSettings';
import { VoiceActivityMonitor } from '@/components/user/VoiceActivityMonitor';
import { Settings, Activity } from 'lucide-react';

export type Language = 'en' | 'de' | 'tr';
export type VoiceStatus = 'idle' | 'listening' | 'processing' | 'speaking';

const Index = () => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [voiceStatus, setVoiceStatus] = useState<VoiceStatus>('idle');
  
  // Real-time metrics state
  const [realTimeMetrics, setRealTimeMetrics] = useState({
    aiPerformance: 94.7,
    responseTime: 1.2,
    customerSatisfaction: 92,
    costSavings: 78,
    activeCallsToday: 1247,
    successRate: 94.2,
    emotionAccuracy: 87.5,
    privacyCompliance: 100
  });
  
  // AI Behavior Settings
  const [responseStyle, setResponseStyle] = useState<'concise' | 'detailed' | 'empathetic'>('empathetic');
  const [personality, setPersonality] = useState<'professional' | 'friendly' | 'formal' | 'casual'>('friendly');
  const [dialect, setDialect] = useState<'us' | 'uk' | 'au' | 'de-de' | 'de-at' | 'de-ch' | 'tr-tr' | 'tr-cy'>('us');
  
  // Voice Activity Settings
  const [isListening, setIsListening] = useState(false);
  const [noiseSuppressionEnabled, setNoiseSuppressionEnabled] = useState(true);

  // Simulate real-time metric updates with more realistic variations
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeMetrics(prev => ({
        ...prev,
        aiPerformance: Math.max(90, Math.min(100, prev.aiPerformance + (Math.random() - 0.5) * 1.5)),
        responseTime: Math.max(0.8, Math.min(2.5, prev.responseTime + (Math.random() - 0.5) * 0.15)),
        customerSatisfaction: Math.max(85, Math.min(100, prev.customerSatisfaction + (Math.random() - 0.5) * 2)),
        activeCallsToday: prev.activeCallsToday + Math.floor(Math.random() * 2),
        successRate: Math.max(90, Math.min(100, prev.successRate + (Math.random() - 0.5) * 0.8)),
        emotionAccuracy: Math.max(80, Math.min(95, prev.emotionAccuracy + (Math.random() - 0.5) * 1.5))
      }));
    }, 3000); // Update every 3 seconds for more realistic feel

    return () => clearInterval(interval);
  }, []);

  const handleConversationUpdate = (conversation: any) => {
    console.log('New conversation entry:', conversation);
    
    // Update voice status based on conversation state
    if (conversation.userInput) {
      setVoiceStatus('processing');
      setTimeout(() => setVoiceStatus('speaking'), 1000);
      setTimeout(() => setVoiceStatus('idle'), 3000);
    }
    
    // Update real-time metrics based on conversation quality
    setRealTimeMetrics(prev => ({
      ...prev,
      customerSatisfaction: Math.min(100, prev.customerSatisfaction + 1),
      successRate: Math.min(100, prev.successRate + 0.5)
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      {/* Enhanced Navigation */}
      <nav className="bg-gray-900/90 backdrop-blur-sm border-b border-gray-700">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <img 
              src="/lovable-uploads/649f40b2-e2bb-4999-95a9-76ba7fc7c5c6.png" 
              alt="Ll-aisolutions Logo" 
              className="w-10 h-10"
            />
            <span className="text-white text-lg font-semibold">Ll-aisolutions Voice AI</span>
            
            {/* Real-time status indicator with dynamic updates */}
            <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full">
              <Activity className="w-3 h-3 text-green-400 animate-pulse" />
              <span className="text-green-300 text-xs">
                {voiceStatus === 'listening' ? 'Listening' : 
                 voiceStatus === 'processing' ? 'Processing' :
                 voiceStatus === 'speaking' ? 'Speaking' : 'Ready'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <LanguageSelector
              currentLanguage={currentLanguage}
              onLanguageChange={setCurrentLanguage}
            />
            
            <Link to="/admin">
              <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700">
                <Settings className="w-4 h-4 mr-2" />
                Admin Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Left Column - Voice Interface */}
          <div className="space-y-6">
            <EnhancedVoiceEngineController
              language={currentLanguage}
              onConversationUpdate={handleConversationUpdate}
            />
            
            {/* AI Behavior Settings */}
            <AIBehaviorSettings
              responseStyle={responseStyle}
              personality={personality}
              dialect={dialect}
              onResponseStyleChange={setResponseStyle}
              onPersonalityChange={setPersonality}
              onDialectChange={setDialect}
            />
            
            {/* Voice Activity Monitor */}
            <VoiceActivityMonitor
              isListening={isListening}
              noiseSuppressionEnabled={noiseSuppressionEnabled}
              onToggleNoiseSuppression={setNoiseSuppressionEnabled}
            />
          </div>

          {/* Right Column - Intelligence & Management */}
          <div className="space-y-6">
            {/* Real-time Interaction Feedback with dynamic status */}
            <RealTimeInteractionFeedback status={voiceStatus} />
            
            {/* Conversation Intelligence */}
            <ConversationIntelligence />
            
            {/* Call Management */}
            <CallManagement />
          </div>
        </div>

        {/* Bottom Section - Enhanced Real-time Performance Metrics */}
        <div className="mt-12 grid md:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-6 rounded-xl text-white hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <h3 className="text-lg font-semibold mb-2">AI Performance</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.aiPerformance.toFixed(1)}%</p>
            <p className="text-blue-100 text-sm">Real-time Accuracy</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-blue-100">Live Updates</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-green-600 to-teal-600 p-6 rounded-xl text-white hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <h3 className="text-lg font-semibold mb-2">Response Time</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.responseTime.toFixed(1)}s</p>
            <p className="text-green-100 text-sm">Average Latency</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-100">Optimized</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-orange-600 to-red-600 p-6 rounded-xl text-white hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <h3 className="text-lg font-semibold mb-2">Customer Satisfaction</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.customerSatisfaction}%</p>
            <p className="text-orange-100 text-sm">Real-time Feedback</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-orange-100">Excellent</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-purple-600 to-pink-600 p-6 rounded-xl text-white hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <h3 className="text-lg font-semibold mb-2">Privacy Compliance</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.privacyCompliance}%</p>
            <p className="text-purple-100 text-sm">GDPR + EU AI Act</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-purple-100">Secure</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
