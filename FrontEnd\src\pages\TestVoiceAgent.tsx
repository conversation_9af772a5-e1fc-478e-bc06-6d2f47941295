import React, { useState } from 'react';
import { VoiceController } from '../components/voice/VoiceController';
import { ComplianceDashboard } from '../components/dashboard/ComplianceDashboard';
import { VoiceStatus, VoiceAnalytics } from '../types/voice/VoiceTypes';

export const TestVoiceAgent: React.FC = () => {
  const [status, setStatus] = useState<VoiceStatus>('idle');
  const [analytics, setAnalytics] = useState<VoiceAnalytics>({
    sessionId: crypto.randomUUID(),
    metrics: {
      startTime: new Date().toISOString(),
    },
    qualityMetrics: {},
    complianceEvents: [],
  });

  const handleStatusChange = (newStatus: VoiceStatus) => {
    setStatus(newStatus);
    setAnalytics((prev) => ({
      ...prev,
      complianceEvents: [
        ...prev.complianceEvents,
        {
          type: 'status_change',
          status: 'info',
          message: `Status changed to ${newStatus}`,
          timestamp: new Date().toISOString(),
        },
      ],
    }));
  };

  const handleCallTransfer = () => {
    setAnalytics((prev) => ({
      ...prev,
      complianceEvents: [
        ...prev.complianceEvents,
        {
          type: 'call_transfer',
          status: 'info',
          message: 'Call transferred to human agent',
          timestamp: new Date().toISOString(),
        },
      ],
    }));
    setStatus('idle');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black p-8">
      <h1 className="text-3xl font-bold text-white mb-6">Test Voice Agent</h1>
      <div className="flex flex-col md:flex-row gap-8">
        <div className="flex-1">
          <VoiceController
            status={status}
            onStatusChange={handleStatusChange}
            onCallTransfer={handleCallTransfer}
          />
        </div>
        <div className="flex-1">
          <ComplianceDashboard sessionId={analytics.sessionId} analytics={analytics} />
        </div>
      </div>
    </div>
  );
}; 