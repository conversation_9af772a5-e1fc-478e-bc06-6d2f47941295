import React from 'react';

export type Language = 'en-US' | 'de-DE' | 'tr-TR';

interface LanguageSelectorProps {
  currentLanguage: Language;
  onLanguageChange: (lang: Language) => void;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({ currentLanguage, onLanguageChange }) => {
  return (
    <div className="flex items-center space-x-2">
      <label className="text-white">Language:</label>
      <select
        value={currentLanguage}
        onChange={(e) => onLanguageChange(e.target.value as Language)}
        className="bg-gray-800 text-white rounded px-2 py-1"
      >
        <option value="en-US">English</option>
        <option value="de-DE">German</option>
        <option value="tr-TR">Turkish</option>
      </select>
    </div>
  );
}; 