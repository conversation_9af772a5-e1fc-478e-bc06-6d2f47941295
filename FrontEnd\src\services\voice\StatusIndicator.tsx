import React from 'react';
import { VoiceStatus } from '../../types/voice/VoiceTypes';

interface StatusIndicatorProps {
  status: VoiceStatus;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status }) => {
  const color =
    status === 'listening' ? 'bg-green-400' :
    status === 'processing' ? 'bg-yellow-400' :
    status === 'speaking' ? 'bg-blue-400' :
    status === 'error' ? 'bg-red-500' :
    'bg-gray-400';

  return (
    <div className="flex items-center space-x-2">
      <span className={`w-3 h-3 rounded-full ${color} animate-pulse`} />
      <span className="text-xs text-white/80">{status}</span>
    </div>
  );
}; 