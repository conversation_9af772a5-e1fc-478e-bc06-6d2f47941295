
export interface Integration {
  id: string;
  name: string;
  category: 'crm' | 'telephony' | 'analytics' | 'support' | 'knowledge' | 'compliance' | 'ai_enhancement' | 'security';
  status: 'connected' | 'disconnected' | 'error' | 'configuring' | 'testing';
  config: { [key: string]: any };
  lastSync?: Date;
  apiEndpoint?: string;
  requiredFields: string[];
  webhookSupport?: boolean;
  rateLimits?: { requests: number; period: string };
  regions?: string[];
}

export const categoryNames = {
  crm: 'Customer Relationship Management',
  telephony: 'Telephony & Communications',
  analytics: 'Analytics & Business Intelligence',
  support: 'Customer Support & Ticketing',
  knowledge: 'Knowledge Management & Search',
  ai_enhancement: 'AI Enhancement & ML Services',
  security: 'Security & Identity Management'
};
