// Voice, analytics, compliance, and status types

export type VoiceStatus = 'idle' | 'listening' | 'processing' | 'speaking' | 'error';

export type VoiceAnalytics = {
  sessionId: string;
  metrics: {
    startTime: string;
    endTime?: string;
    duration?: number;
    audioDuration?: number;
    processingTime?: number;
    confidence?: number;
    errors?: number;
    latency?: number;
    recognitionAccuracy?: number;
  };
  qualityMetrics: {
    audioQuality?: number;
    recognitionAccuracy?: number;
    latency?: number;
  };
  complianceEvents: ComplianceEvent[];
};

export type ComplianceEvent = {
  type: string;
  status: 'success' | 'error' | 'info';
  message: string;
  timestamp: string;
}; 