
import { 
  Users, 
  Phone, 
  BarChart3,
  MessageSquare, 
  Database, 
  Brain,
  Shield,
  Settings,
  CheckCircle,
  AlertCircle,
  TestTube,
  Zap
} from 'lucide-react';

export const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'crm':
      return <Users className="w-5 h-5" />;
    case 'telephony':
      return <Phone className="w-5 h-5" />;
    case 'analytics':
      return <BarChart3 className="w-5 h-5" />;
    case 'support':
      return <MessageSquare className="w-5 h-5" />;
    case 'knowledge':
      return <Database className="w-5 h-5" />;
    case 'ai_enhancement':
      return <Brain className="w-5 h-5" />;
    case 'security':
      return <Shield className="w-5 h-5" />;
    default:
      return <Settings className="w-5 h-5" />;
  }
};

export const getStatusIcon = (status: string) => {
  switch (status) {
    case 'connected':
      return <CheckCircle className="w-4 h-4 text-green-400" />;
    case 'error':
      return <AlertCircle className="w-4 h-4 text-red-400" />;
    case 'configuring':
      return <Settings className="w-4 h-4 text-yellow-400 animate-spin" />;
    case 'testing':
      return <TestTube className="w-4 h-4 text-blue-400 animate-pulse" />;
    default:
      return <AlertCircle className="w-4 h-4 text-gray-500" />;
  }
};

export const getTestIcon = (status: string) => {
  switch (status) {
    case 'success':
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    case 'error':
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    case 'testing':
      return <Zap className="w-4 h-4 text-yellow-500 animate-pulse" />;
    default:
      return <Zap className="w-4 h-4 text-gray-500" />;
  }
};
